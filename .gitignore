# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
.git-back

docker-compose.override.yml

# dependencies
node_modules
*.code-workspace
.pnp
.pnp.js

# testing
coverage
.nyc_output

# next.js
.next/
out/
next-env.d.ts

# nitro
.nitro/
.output/

# production
build

# misc
.DS_Store
*.pem
.idea

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env.*
!.env.default

# vercel
.vercel

# typescript
dist/
.cache

# turbo
.turbo

CLAUDE.user.md
CLAUDE.local.md
.gemini

# resources
resources/contexts
resources/tickets
resources/prompts
resources/temp

**/.claude/settings.local.json
.claude/user-agents

# @todo eventually remove this, needs to be versioned, but need to extract env variables out
.mcp.json
