import { NestFactory, Reflector } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from 'nestjs-pino';

import { AppModule } from './app.module';

import './tracer'; // must come before importing any instrumented module.

import {
  ClassSerializerInterceptor,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as cookieParser from 'cookie-parser';

const logLevels = ['verbose', 'debug', 'info', 'log', 'warn', 'error'] as const;
type LogLevel = (typeof logLevels)[number];

const logLevelAndAbove = (startLevel?: LogLevel): LogLevel[] => {
  if (!startLevel || !logLevels.includes(startLevel)) {
    return [...logLevels];
  }
  const index = logLevels.indexOf(startLevel);
  return logLevels.slice(index);
};

const allowedOrigins = [
  process.env.PATIENTS_URL,
  process.env.DOCTORS_URL,
  process.env.ADMIN_URL,
];

if (process.env.ENVIRONMENT === 'production') {
  allowedOrigins.push('https://startwillow.com');
  allowedOrigins.push('https://www.startwillow.com');
}

console.log(`[cors] Allowed origins: ${allowedOrigins.join(', ')}`);

async function bootstrap() {
  const logLevels = logLevelAndAbove(process.env.LOG_LEVEL as LogLevel);
  console.log(`Using log levels: ${logLevels.join(', ')}`);

  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: logLevels as any[],
    rawBody: true,
    bufferLogs: true,
  });

  if (process.env.ENVIRONMENT !== 'local') app.useLogger(app.get(Logger));

  app.useBodyParser('text');

  // Configure CORS to allow credentials from frontend apps
  app.enableCors({
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or Postman)
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.warn(`CORS request from disallowed origin: ${origin}`);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    exposedHeaders: ['set-cookie'],
  });

  //setup cookies with encryption
  app.use(cookieParser(process.env.APP_ENCRYPTION_KEY));

  // Enable API versioning
  app.enableVersioning({
    type: VersioningType.URI,
  });

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Intercept all responses and serialize the response body
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));

  const config = new DocumentBuilder()
    .setTitle('Willow API')
    .setDescription('The Willow API description')
    .setVersion('1.0')
    .addTag('willow')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const host = process.env.API_HOST ?? '0.0.0.0';
  const port = +(process.env.API_PORT ?? 8080);
  console.log(`Listening on ${host}:${port}`);
  await app.listen(port, host);
}

bootstrap().catch((error) => {
  console.error(error);
  setTimeout(() => {
    process.exit(1);
  }, 5_000); // Wait for 5 seconds before exiting to allow logs to flush
});
