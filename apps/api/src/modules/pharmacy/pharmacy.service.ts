import { runInDbTransaction } from '@/helpers/transaction';
import { CacheService } from '@modules/cache/cache.service';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@modules/prisma/prisma.service';
import {
  segmentIdentifyEvent,
  segmentTrackEvents,
} from '@modules/shared/events';
import {
  getProductForAnalytics,
  getProductTypeFromGenericName,
} from '@modules/shared/helpers/generic';
import { SegmentIdentify, SegmentTrack } from '@modules/shared/types/events';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Prisma } from '@prisma/client';
import { isAfter } from 'date-fns';

import { AuditService } from '../audit-log/audit-log.service';
import { roles } from '../auth/types/roles';
import { SendMessageUseCase } from '../chat/use-cases/send-message.use-case';
import { DosespotService } from '../dosespot/dosespot.service';
import { NonRetriableError } from '../shared/errors/non-retriable.error';
import { RetriableError } from '../shared/errors/retriable.error';
import {
  TreatmentActor,
  TreatmentService,
  TreatmentSnapshot,
} from '../treatment/services/treatment.service';
import { TreatmentMachineContext } from '../treatment/states/treatment.state';
import { CreatePharmacyDto } from './dto/create-pharmacy.dto';
import { UpdatePharmacyDto } from './dto/update-pharmacy.dto';

type TreatmentWithRelations = Prisma.TreatmentGetPayload<{
  include: {
    initialProductPrice: {
      include: {
        product: {
          include: {
            pharmacy: true;
          };
        };
        equivalenceGroup: {
          include: {
            productPrices: {
              include: {
                product: true;
              };
            };
          };
        };
      };
    };
    topProductPrice: true;
    doctor: true;
    patient: true;
  };
}>;

export enum TreatmentTransferScenario {
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS_NO_REFILLS = 'IN_PROGRESS_NO_REFILLS',
  IN_PROGRESS_WITH_REFILLS = 'IN_PROGRESS_WITH_REFILLS',
  FAILED_PAYMENT = 'FAILED_PAYMENT',
  PAUSED_INDEFINITELY = 'PAUSED_INDEFINITELY',
  PAUSED_UNTIL_DATE = 'PAUSED_UNTIL_DATE',
  COMPLETED_OR_CANCELLED = 'COMPLETED_OR_CANCELLED',
  NO_EQUIVALENT_PRODUCT = 'NO_EQUIVALENT_PRODUCT',
  MULTI_VIAL = 'MULTI_VIAL',
}

@Injectable()
export class PharmacyService {
  private readonly logger = new Logger(PharmacyService.name);
  private readonly CACHE_FRESH = 3600; // 1 hour
  private readonly CACHE_STALE = 86400; // 1 day

  constructor(
    private readonly prisma: PrismaService,
    private readonly cacheService: CacheService,
    private readonly dosespotService: DosespotService,
    private readonly treatmentService: TreatmentService,
    private readonly eventEmitter: EventEmitter2,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly auditService: AuditService,
  ) {}

  /**
   * Get patient count for a specific pharmacy with caching
   * Uses flexible cache with stale-while-revalidate pattern
   */
  private async getPatientCountForPharmacy(
    pharmacyId: string,
  ): Promise<number> {
    const cacheKey = `pharmacy:patient-count:${pharmacyId}`;

    return this.cacheService.flexible<number>(
      cacheKey,
      [this.CACHE_FRESH, this.CACHE_STALE],
      async () => {
        return this.prisma.patient.count({
          where: {
            pharmacyId,
            user: { deletedAt: null },
          },
        });
      },
    );
  }

  /**
   * Get patient counts by state for a pharmacy with caching
   * Uses flexible cache with stale-while-revalidate pattern
   */
  private async getCachedPatientCountsByState(pharmacyId: string) {
    const cacheKey = `pharmacy:state-patient-counts:${pharmacyId}`;

    return this.cacheService.flexible<{
      statePatientCounts: any[];
      totalPatientCount: number;
    }>(cacheKey, [this.CACHE_FRESH, this.CACHE_STALE], async () => {
      // Check if pharmacy exists
      const pharmacy = await this.prisma.pharmacy.findUnique({
        where: { id: pharmacyId },
        include: {
          PharmacyOnState: {
            include: { state: true },
          },
        },
      });

      if (!pharmacy) {
        throw new Error(`Pharmacy with ID ${pharmacyId} not found`);
      }

      // Get all states the pharmacy serves
      const stateIds = pharmacy.PharmacyOnState.map((pos) => pos.stateId);

      // Get the count of patients grouped by state in a single query for ALL states
      const patientsByState = await this.prisma.patient.groupBy({
        by: ['stateId'],
        where: { pharmacyId },
        _count: { stateId: true },
      });

      // Create a map of state IDs to patient counts
      const stateCountMap = new Map(
        patientsByState.map((state) => [state.stateId, state._count.stateId]),
      );

      // Get all states that have patients but are not currently associated with the pharmacy
      const statesWithPatientsNotInPharmacy = patientsByState.filter(
        (state) => !stateIds.includes(state.stateId),
      );

      // Fetch details for these states
      let additionalStates = [];
      if (statesWithPatientsNotInPharmacy.length > 0) {
        const additionalStateIds = statesWithPatientsNotInPharmacy.map(
          (state) => state.stateId,
        );

        additionalStates = await this.prisma.state.findMany({
          where: { id: { in: additionalStateIds } },
        });
      }

      // Map the counts to state information
      const statePatientCounts = [
        // First, include states that are currently associated with the pharmacy
        ...pharmacy.PharmacyOnState.map((stateInfo) => {
          const patientCount = stateCountMap.get(stateInfo.stateId) || 0;

          return {
            stateId: stateInfo.stateId,
            stateName: stateInfo.state.name,
            stateCode: stateInfo.state.code,
            patientCount,
            enabled: stateInfo.state.enabled,
            isAssociated: true,
          };
        }),
        // Then, include states that have patients but are not associated with the pharmacy
        ...additionalStates.map((state) => {
          const patientCount = stateCountMap.get(state.id) || 0;

          return {
            stateId: state.id,
            stateName: state.name,
            stateCode: state.code,
            patientCount,
            enabled: state.enabled,
            isAssociated: false,
          };
        }),
      ];

      // Get total count of patients for this pharmacy
      const totalPatientCount = await this.prisma.patient.count({
        where: {
          pharmacyId,
          user: { deletedAt: null },
        },
      });

      return {
        statePatientCounts,
        totalPatientCount,
      };
    });
  }

  async findAll(options?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    direction?: 'asc' | 'desc';
    showInactive?: boolean;
    showPatients?: boolean;
    inStateId?: string;
  }) {
    const {
      page = 1,
      limit = 10,
      search = '',
      sortBy = 'name',
      direction = 'asc',
      showInactive = false,
      showPatients = false,
      inStateId,
    } = options || {};

    // Create the where clause for filtering
    let where: Prisma.PharmacyWhereInput = {};

    // If showInactive is false, only show enabled pharmacies
    if (!showInactive) {
      where.enabled = true;
    }

    if (search) {
      where = {
        ...where, // Keep the enabled filter
        OR: [
          { name: { contains: search, mode: Prisma.QueryMode.insensitive } },
          {
            doseSpotPharmacyId: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            PharmacyOnState: {
              some: {
                state: {
                  OR: [
                    {
                      name: {
                        contains: search,
                        mode: Prisma.QueryMode.insensitive,
                      },
                    },
                    {
                      code: {
                        contains: search,
                        mode: Prisma.QueryMode.insensitive,
                      },
                    },
                  ],
                },
              },
            },
          },
        ],
      };
    }

    if (inStateId) {
      where = {
        ...where,
        PharmacyOnState: {
          ...(where.PharmacyOnState || {}),
          some: {
            stateId: inStateId,
          },
        },
      };
    }

    // Get total count for pagination
    const total = await this.prisma.pharmacy.count({ where });

    // Determine sort field and direction
    let orderBy: Prisma.PharmacyOrderByWithRelationInput = { name: direction };

    // Handle different sort fields
    if (
      sortBy === 'name' ||
      sortBy === 'doseSpotPharmacyId' ||
      sortBy === 'enabled' ||
      sortBy === 'regularPriority' ||
      sortBy === 'usingGLP1Priority' ||
      sortBy === 'color' ||
      sortBy === 'slug' ||
      sortBy === 'createdAt'
    ) {
      orderBy = { [sortBy]: direction };
    }

    // Handle legacy 'priority' sort field
    if (sortBy === 'priority') {
      orderBy = { regularPriority: direction };
    }

    // Fetch paginated pharmacies
    const pharmacies = await this.prisma.pharmacy.findMany({
      where,
      include: {
        PharmacyOnState: {
          include: { state: { select: { id: true, name: true, code: true } } },
        },
        Product: { select: { id: true, name: true } },
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    // If showPatients is true, count active patients for each pharmacy
    if (showPatients) {
      const pharmaciesWithPatientCounts = await Promise.all(
        pharmacies.map(async (pharmacy) => {
          // Use cached patient count for better performance
          const patientCount = await this.getPatientCountForPharmacy(
            pharmacy.id,
          );

          return {
            ...pharmacy,
            patientCount,
          };
        }),
      );

      return {
        pharmacies: pharmaciesWithPatientCounts,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    }

    return {
      pharmacies,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const pharmacy = await this.prisma.pharmacy.findUnique({
      where: { id },
      include: {
        PharmacyOnState: { include: { state: true } },
        Product: { orderBy: { order: 'asc' } },
      },
    });

    if (!pharmacy) return null;

    // Use cached patient count for better performance
    const patientCount = await this.getPatientCountForPharmacy(pharmacy.id);

    return {
      ...pharmacy,
      patientCount: patientCount,
    };
  }

  async create(data: CreatePharmacyDto) {
    const { stateIds, ...pharmacyData } = data;

    // Generate slug from name
    const slug = pharmacyData.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    return this.prisma.pharmacy.create({
      data: {
        ...pharmacyData,
        slug,
        PharmacyOnState: {
          create:
            stateIds?.map((stateId) => ({
              stateId,
            })) || [],
        },
      },
      include: { PharmacyOnState: { include: { state: true } }, Product: true },
    });
  }

  async update(id: string, data: UpdatePharmacyDto) {
    const { stateIds, ...pharmacyData } = data;

    // If stateIds is provided, first delete existing relationships
    if (stateIds) {
      await this.prisma.pharmacyOnState.deleteMany({
        where: { pharmacyId: id },
      });
    }

    // Remove slug from update data to prevent editing it
    // Slug should only be set during creation

    return this.prisma.pharmacy.update({
      where: { id },
      data: {
        ...pharmacyData,
        ...(stateIds && {
          PharmacyOnState: {
            create: stateIds.map((stateId) => ({
              stateId,
            })),
          },
        }),
      },
      include: { PharmacyOnState: { include: { state: true } }, Product: true },
    });
  }

  async delete(id: string) {
    // Check if pharmacy exists
    const pharmacy = await this.findOne(id);

    if (!pharmacy) {
      throw new Error(`Pharmacy with ID ${id} not found`);
    }

    // Check if pharmacy has patients assigned to it
    const patientsCount = await this.prisma.patient.count({
      where: {
        pharmacyId: id,
      },
    });

    if (patientsCount > 0) {
      throw new Error(
        `Cannot delete pharmacy: ${patientsCount} patients are assigned to this pharmacy. Reassign these patients first.`,
      );
    }

    // First delete related records in PharmacyOnState
    await this.prisma.pharmacyOnState.deleteMany({
      where: { pharmacyId: id },
    });

    return this.prisma.pharmacy.delete({
      where: { id },
    });
  }

  async deactivate(id: string) {
    // Check if pharmacy exists
    const pharmacy = await this.findOne(id);

    if (!pharmacy) {
      throw new Error(`Pharmacy with ID ${id} not found`);
    }

    // Check if pharmacy has patients with active treatments
    const patientsWithActiveTreatments = await this.prisma.patient.count({
      where: {
        pharmacyId: id,
        treatment: {
          some: {
            // Active treatments are those that are not completed or uncollectible
            AND: [
              { status: { notIn: ['completed', 'uncollectible'] } },
              { deletedAt: null },
            ],
          },
        },
      },
    });

    if (patientsWithActiveTreatments > 0) {
      throw new Error(
        `Cannot deactivate pharmacy: ${patientsWithActiveTreatments} patients have active treatments`,
      );
    }

    return this.prisma.pharmacy.update({
      where: { id },
      data: { enabled: false },
      include: { PharmacyOnState: { include: { state: true } }, Product: true },
    });
  }

  async reactivate(id: string) {
    // Check if pharmacy exists
    const pharmacy = await this.findOne(id);

    if (!pharmacy) {
      throw new Error(`Pharmacy with ID ${id} not found`);
    }

    return this.prisma.pharmacy.update({
      where: { id },
      data: { enabled: true },
      include: { PharmacyOnState: { include: { state: true } }, Product: true },
    });
  }

  /**
   * Sends a doctor note to inform about a treatment during pharmacy transfer
   * @param prisma - Prisma transaction client
   * @param treatment - The treatment being transferred
   * @param targetPharmacyName - The name of the target pharmacy
   * @param wasCancelled - Whether the treatment was cancelled (true for paused) or transferred
   * @param vialCount - Optional vial count for multi-vial treatments that cannot be transferred
   * @param noEquivalent - Whether the treatment couldn't be transferred due to no equivalent product
   */
  private async sendDoctorNoteForTreatmentTransfer(
    prisma: PrismaTransactionalClient,
    treatment: TreatmentWithRelations,
    targetPharmacyName: string,
    wasCancelled: boolean = false,
    vialCount?: number,
    noEquivalent?: boolean,
  ) {
    try {
      // Get the conversation and doctor information
      const patient = await prisma.patient.findFirst({
        where: { id: treatment.patientId },
        select: {
          conversations: {
            where: { type: 'patientDoctor' },
            select: { id: true, type: true },
          },
          doctor: {
            select: {
              userId: true,
              user: { select: { firstName: true, lastName: true } },
            },
          },
        },
      });

      const patientDoctorConversation = patient?.conversations?.find(
        (c) => c.type === 'patientDoctor',
      );
      if (!patientDoctorConversation?.id || !patient?.doctor?.userId) {
        this.logger.warn(
          `Could not send doctor note: conversation or doctor not found for patient ${treatment.patientId}`,
        );
        return;
      }

      const conversationId = patientDoctorConversation.id;
      const doctorUserId = patient.doctor.userId;
      const productName =
        treatment.initialProductPrice?.product?.name || 'medication';

      // Create the doctor note message based on treatment state and action
      let messageContent: string;

      if (vialCount && vialCount > 1) {
        // For multi-vial treatments that cannot be transferred
        messageContent = `Patient's treatment for ${productName} with ${vialCount} vials could not be transferred to ${targetPharmacyName} because there is no equivalent product with the same number of vials available at the new pharmacy. The remaining refills have been removed from the current treatment. Please prescribe a new treatment if the patient wishes to continue.`;
      } else if (noEquivalent) {
        // For treatments that were cancelled due to no equivalent product
        messageContent = `Patient's treatment for ${productName} could not be transferred to ${targetPharmacyName} because there is no equivalent product available at the new pharmacy. The treatment has been cancelled and the patient will need a new prescription if they wish to continue treatment.`;
      } else if (wasCancelled) {
        // For treatments that were paused indefinitely
        messageContent = `Treatment for ${productName} was in paused state and has been cancelled during pharmacy transfer to ${targetPharmacyName}. The treatment could not be recreated in the new pharmacy because it was paused. The patient will need a new prescription if they wish to continue treatment.`;
      }

      // Use the SendMessageUseCase to send the message
      await this.sendMessageUseCase.execute(
        {
          conversationId,
          userId: doctorUserId,
          content: messageContent,
          contentType: 'text',
          type: 'doctorNote',
          role: roles.Doctor,
          needsReply: false,
        },
        { prisma },
      );
    } catch (error) {
      // Log the error but don't throw it to avoid disrupting the pharmacy transfer process
      this.logger.error(
        `Error sending doctor note for treatment transfer: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Get patient counts grouped by state for a specific pharmacy
   * @param id Pharmacy ID
   * @returns Object with state information and patient counts
   */
  async getPatientCountsByState(id: string) {
    // Use the cached version for better performance
    return this.getCachedPatientCountsByState(id);
  }

  /**
   * Resolves the best pharmacy for a patient based on their GLP-1 status and state
   * This method also handles persisting the pharmacy ID to the patient record
   *
   * @param patientId - The patient's ID
   * @param stateId - The patient's state ID
   * @param categoryId - The product category ID
   * @param onboardingSnapshot - The current onboarding snapshot
   * @returns The best pharmacy for the patient or null if no suitable pharmacy is found
   */
  async resolvePharmacyPriority(
    patientId: string,
    stateId: string,
    categoryId: string,
    onboardingSnapshot?: any,
  ) {
    // First check current pharmacy if it exists
    const currentPatient = await this.prisma.patient.findUnique({
      where: { id: patientId },
      select: { pharmacyId: true },
    });

    // Extract GLP-1 status from questionnaire context if available
    let usingGLP1: 'yes' | 'no' = 'no';
    if (onboardingSnapshot?.context?.questionnaire?.usingGLP1) {
      usingGLP1 = onboardingSnapshot.context.questionnaire.usingGLP1;
    }

    // Determine which priority field to use based on GLP-1 status
    const priorityField =
      usingGLP1 === 'yes' ? 'usingGLP1Priority' : 'regularPriority';

    // Build the where clause for filtering pharmacies
    const where: Prisma.PharmacyWhereInput = {
      enabled: true,
      PharmacyOnState: { some: { stateId } },
    };

    // Filter pharmacies that have active, core products available in onboarding with the specified category
    where.Product = {
      some: {
        productCategories: {
          some: {
            productCategoryId: { equals: categoryId },
          },
        },
        active: true,
        isCore: true,
        isAvailableInOnboarding: true,
      },
    };

    // Find the pharmacy with the highest priority for the given criteria
    const pharmacy = await this.prisma.pharmacy.findFirst({
      where,
      orderBy: { [priorityField]: 'desc' },
      include: {
        PharmacyOnState: { include: { state: true } },
        Product: true,
      },
    });

    // If a pharmacy was found, update the patient's pharmacy in the database
    if (
      pharmacy &&
      (!currentPatient?.pharmacyId || currentPatient.pharmacyId !== pharmacy.id)
    ) {
      await this.prisma.patient.update({
        where: { id: patientId },
        data: { pharmacyId: pharmacy.id },
      });

      this.logger.log(
        `Updated patient ${patientId} with pharmacy ${pharmacy.name} (${pharmacy.id})`,
      );
    }

    return pharmacy;
  }

  /**
   * Converts productType to the expected audit log format
   */
  private convertProductTypeForAudit(
    productType: string,
  ): 'Semaglutide' | 'Tirzepatide' | 'Other' {
    const normalizedType = productType.toLowerCase();
    if (normalizedType === 'semaglutide') {
      return 'Semaglutide';
    }
    if (normalizedType === 'tirzepatide') {
      return 'Tirzepatide';
    }
    return 'Other';
  }

  /**
   * Transfers a patient to a new pharmacy
   * This method handles updating the patient's pharmacy in the database,
   * updating DoseSpot pharmacy association, and updating any related treatments.
   *
   * @param newPharmacyId - ID of the target pharmacy
   * @param patientId - ID of the patient to transfer
   * @param bulkTransferId - Optional ID of the bulk transfer this is part of
   * @param prisma
   * @returns A Promise that resolves to an object with success status, message, and transfer count
   * @throws NonRetriableError for validation or business logic errors
   * @throws RetriableError for database errors or DoseSpot API failures
   */
  async transferPatient(
    newPharmacyId: string,
    patientId: string,
    bulkTransferId?: string,
    prisma?: PrismaTransactionalClient,
  ) {
    // Use transaction to ensure all operations succeed or fail together
    return runInDbTransaction(prisma || this.prisma, async (tx) => {
      try {
        // Fetch patient with related data
        const patient = await tx.patient.findUnique({
          where: { id: patientId },
          include: {
            user: { select: { id: true, email: true } },
            doctor: {
              select: {
                id: true,
                doseSpotClinicianId: true,
                user: { select: { firstName: true, lastName: true } },
              },
            },
            pharmacy: { select: { id: true, name: true } },
            state: { select: { id: true, code: true } },
          },
        });

        if (!patient) {
          throw new NonRetriableError('Patient not found');
        }

        // For new patients without a DoseSpot ID or doctor, we'll just update the pharmacy ID
        const isNewPatient = !patient.doseSpotPatientId || !patient.doctor;

        // Fetch target pharmacy with related data
        const targetPharmacy = await tx.pharmacy.findUnique({
          where: { id: newPharmacyId, enabled: true },
          include: {
            PharmacyOnState: {
              include: { state: true },
            },
          },
        });

        if (!targetPharmacy) {
          throw new NonRetriableError(
            'Target pharmacy not found or not enabled',
          );
        }

        // Check if the patient's state is allowed for the target pharmacy
        const allowedStates = new Set(
          targetPharmacy.PharmacyOnState.map((pos) => pos.stateId),
        );

        if (!allowedStates.has(patient.state.id)) {
          throw new NonRetriableError(
            `Target pharmacy does not service the patient's state`,
          );
        }

        // Check if the patient is already assigned to this pharmacy
        if (patient.pharmacy?.id === targetPharmacy.id) {
          throw new NonRetriableError(
            `Patient ${patientId} is already assigned to pharmacy ${newPharmacyId}. No transfer needed.`,
          );
        }

        // 2. Update patient's pharmacy in database (we'll set DoseSpot pharmacy after all DB operations)
        await tx.patient.update({
          where: { id: patientId },
          data: { pharmacyId: newPharmacyId },
        });

        // Emit Segment identify event to update defaultPharmacy
        const updatePharmacyIdentifyEvent: SegmentIdentify = {
          userId: patient.user.id,
          traits: {
            defaultPharmacy: targetPharmacy.name,
          },
        };
        this.eventEmitter.emit(
          segmentIdentifyEvent.analyticIdentify,
          updatePharmacyIdentifyEvent,
        );

        // 3. Find all treatments for the patient
        const allTreatments = await tx.treatment.findMany({
          where: {
            patientId,
          },
          include: {
            initialProductPrice: {
              include: {
                product: {
                  include: {
                    pharmacy: true,
                  },
                },
                equivalenceGroup: {
                  include: {
                    productPrices: {
                      include: {
                        product: true,
                      },
                    },
                  },
                },
              },
            },
            topProductPrice: true,
            doctor: true,
            patient: true,
          },
        });

        const transferResults = [];
        const treatmentErrors: Array<{
          treatmentId: string;
          scenario: string;
          error: string;
          canContinue: boolean;
        }> = [];
        const scenarioSummary: Record<TreatmentTransferScenario, number> = {
          [TreatmentTransferScenario.SCHEDULED]: 0,
          [TreatmentTransferScenario.IN_PROGRESS_NO_REFILLS]: 0,
          [TreatmentTransferScenario.IN_PROGRESS_WITH_REFILLS]: 0,
          [TreatmentTransferScenario.FAILED_PAYMENT]: 0,
          [TreatmentTransferScenario.PAUSED_INDEFINITELY]: 0,
          [TreatmentTransferScenario.PAUSED_UNTIL_DATE]: 0,
          [TreatmentTransferScenario.COMPLETED_OR_CANCELLED]: 0,
          [TreatmentTransferScenario.NO_EQUIVALENT_PRODUCT]: 0,
          [TreatmentTransferScenario.MULTI_VIAL]: 0,
        };

        // 4. Process each treatment
        for (const treatment of allTreatments) {
          try {
            // Get the actor for the treatment
            const actor = await this.treatmentService.getActor(
              treatment.id,
              () => {},
              treatment.state as TreatmentSnapshot,
            );

            // Get treatment state and context
            const actorSnapshot = actor.getSnapshot();
            const context = actorSnapshot.context;
            const treatmentState = this.treatmentService.getState(actor);

            // Determine which scenario applies
            const scenario = this.determineTreatmentScenario(
              treatment,
              treatmentState,
              context,
            );

            scenarioSummary[scenario]++;

            // Handle based on scenario
            let result: {
              success: boolean;
              newTreatmentId?: string;
              error?: string;
              treatmentId?: string;
            };

            switch (scenario) {
              case TreatmentTransferScenario.COMPLETED_OR_CANCELLED:
                // Skip completed or cancelled treatments
                this.logger.log(
                  `Skipping treatment ${treatment.id} - already ${treatment.status}`,
                );
                continue;

              case TreatmentTransferScenario.IN_PROGRESS_NO_REFILLS:
                result = await this.handleInProgressNoRefills();
                break;

              case TreatmentTransferScenario.IN_PROGRESS_WITH_REFILLS:
                // Check if can transfer first
                if (
                  !actor
                    .getSnapshot()
                    .can({ type: 'transfer', treatmentId: treatment.id })
                ) {
                  this.logger.warn(
                    `Treatment ${treatment.id} cannot be transferred - state machine disallows transfer`,
                  );
                  treatmentErrors.push({
                    treatmentId: treatment.id,
                    scenario: scenario,
                    error: 'State machine disallows transfer',
                    canContinue: true,
                  });
                  continue;
                }
                result = await this.handleInProgressWithRefills(
                  treatment,
                  targetPharmacy,
                  actor,
                  context,
                  tx,
                );
                break;

              case TreatmentTransferScenario.SCHEDULED:
                result = await this.handleScheduledTreatment(
                  treatment,
                  targetPharmacy,
                  actor,
                  context,
                  tx,
                );
                break;

              // -> cancel and note
              case TreatmentTransferScenario.PAUSED_INDEFINITELY:
                result = await this.handlePausedIndefinitely(
                  treatment,
                  targetPharmacy,
                  actor,
                  tx,
                );
                break;

              // remove next refills if exist, mark it as transferred, re-create in new pharmacy and schedule it
              case TreatmentTransferScenario.PAUSED_UNTIL_DATE:
                result = await this.handlePausedUntilDate(
                  treatment,
                  targetPharmacy,
                  actor,
                  context,
                  tx,
                );
                break;

              // expire them after 30 days
              // mark uncollectible after 30 days
              case TreatmentTransferScenario.FAILED_PAYMENT:
                result = await this.handleFailedPayment(
                  treatment,
                  targetPharmacy,
                  actor,
                  context,
                  tx,
                );
                break;

              //   cancel and note
              case TreatmentTransferScenario.MULTI_VIAL:
                result = await this.handleMultiVialTreatment(
                  treatment,
                  targetPharmacy,
                  actor,
                  context,
                  tx,
                );
                break;

              // NOTE: NO_EQUIVALENT_PRODUCT is handled within other scenarios
              // when findEquivalentProduct returns null

              default:
                // Unknown scenario is a critical bug that should stop everything
                throw new NonRetriableError(
                  `Unknown treatment scenario: ${scenario}`,
                );
            }

            if (result.success) {
              transferResults.push(result);

              // Emit prescription transferred event
              const doctorName = patient.doctor?.user
                ? `${patient.doctor.user.firstName} ${patient.doctor.user.lastName}`
                : 'Unknown Doctor';

              const genericName =
                treatment.initialProductPrice?.product?.genericName ||
                'semaglutide';
              const productType = getProductTypeFromGenericName(genericName);
              const productForm =
                treatment.initialProductPrice?.product?.form || 'injectable';

              const status = result.newTreatmentId ? 'Complete' : 'Passed';

              // Create audit log
              await this.auditService.append(
                {
                  action: 'PRESCRIPTION_TRANSFERRED',
                  actorType: 'SYSTEM',
                  actorId: 'pharmacy-transfer-service',
                  resourceType: 'PATIENT',
                  resourceId: patient.id,
                  patientId: patient.id,
                  details: {
                    state: patient.state.code,
                    doctorName,
                    oldPharmacy: patient.pharmacy?.name || 'Unknown',
                    newPharmacy: targetPharmacy.name,
                    productType: this.convertProductTypeForAudit(productType),
                    productForm,
                    status,
                  },
                },
                { prisma: tx },
              );
            } else if (!result.success && result.error) {
              // Handler returned failure - add to treatment errors
              treatmentErrors.push({
                treatmentId: result.treatmentId || treatment.id,
                scenario: scenario,
                error: result.error,
                canContinue: true,
              });
            }
          } catch (error) {
            // Log treatment-specific errors but continue with patient transfer
            this.logger.warn(
              `Failed to transfer treatment ${treatment.id}: ${error.message}`,
              error.stack,
            );

            treatmentErrors.push({
              treatmentId: treatment.id,
              scenario: 'unknown',
              error: error.message,
              canContinue: true,
            });

            // Only re-throw critical errors that should stop the entire transfer
            if (
              error instanceof NonRetriableError &&
              error.message.includes('Unknown treatment scenario')
            ) {
              throw error;
            }
          }
        }

        // Now that all SQL operations have completed successfully, update DoseSpot pharmacy
        // This should be the last operation, and if it fails, it should be retriable
        if (!isNewPatient) {
          try {
            await this.dosespotService.addPatientToPharmacy(
              patient.doseSpotPatientId,
              targetPharmacy.doseSpotPharmacyId,
              patient.doctor.doseSpotClinicianId,
            );
          } catch (error) {
            // If DoseSpot API call fails, throw a RetriableError to retry the whole operation
            throw new RetriableError(`DoseSpot API error: ${error.message}`);
          }

          // Emit segment event
          const pharmacyMigratedEvent: SegmentTrack = {
            event: segmentTrackEvents.pharmacyMigrated.name,
            userId: patient.user.id,
            properties: {
              from: patient.pharmacy?.name || 'No previous pharmacy',
              to: targetPharmacy.name,
            },
          };

          this.eventEmitter.emit(
            segmentTrackEvents.pharmacyMigrated.event,
            pharmacyMigratedEvent,
          );
        }

        // If this is part of a bulk transfer, increment the completedJobs counter
        if (bulkTransferId) {
          const updatedTransfer = await tx.bulkTransfer.update({
            where: {
              id: bulkTransferId,
              type: 'pharmacy',
            },
            data: { completedJobs: { increment: 1 } },
            select: { completedJobs: true, queuedJobs: true, status: true },
          });

          // Check if all jobs are completed and update status if needed
          if (updatedTransfer.completedJobs === updatedTransfer.queuedJobs) {
            await tx.bulkTransfer.update({
              where: {
                id: bulkTransferId,
                type: 'pharmacy',
              },
              data: {
                status: 'completed',
                completedAt: new Date(),
              },
            });
          }
        }

        // Log summary if there were treatment errors
        if (treatmentErrors.length > 0) {
          this.logger.warn(
            `Patient ${patientId} transferred to ${targetPharmacy.name} but ${treatmentErrors.length} treatments failed to transfer:`,
            treatmentErrors
              .map((e) => `${e.treatmentId}: ${e.error}`)
              .join(', '),
          );
        }

        return {
          success: true,
          message: `Patient transferred successfully to ${targetPharmacy.name}. Processed ${allTreatments.length} treatments with ${transferResults.length} transfers.`,
          transferCount: transferResults.length,
          scenarioSummary,
          treatmentErrors:
            treatmentErrors.length > 0 ? treatmentErrors : undefined,
        };
      } catch (error) {
        // Handle Prisma database errors as retriable
        if (
          error instanceof Prisma.PrismaClientKnownRequestError ||
          error instanceof Prisma.PrismaClientUnknownRequestError ||
          error instanceof Prisma.PrismaClientRustPanicError ||
          error instanceof Prisma.PrismaClientInitializationError
        ) {
          throw new RetriableError(
            `Database operation failed: ${error.message}`,
          );
        }

        // If it's a RetriableError, just rethrow it
        if (error instanceof RetriableError) {
          throw error;
        }

        // Re-throw if already classified as NonRetriableError
        if (error instanceof NonRetriableError) {
          throw error;
        }

        // By default, treat unclassified errors as non-retriable
        throw new NonRetriableError(`Unclassified error: ${error.message}`);
      }
    });
  }

  /**
   * Determines which transfer scenario applies to a treatment
   *
   * Note: NO_EQUIVALENT_PRODUCT is not returned by this method.
   * It is handled dynamically within other scenarios when
   * findEquivalentProduct() returns null.
   */
  private determineTreatmentScenario(
    treatment: TreatmentWithRelations,
    treatmentState: string,
    context: TreatmentMachineContext,
  ): TreatmentTransferScenario {
    // Check for completed or cancelled treatments first
    if (
      ['completed', 'cancelled', 'uncollectible'].includes(treatment.status)
    ) {
      return TreatmentTransferScenario.COMPLETED_OR_CANCELLED;
    }

    // Check for multi-vial treatments
    if (context?.vials && context.vials > 1) {
      return TreatmentTransferScenario.MULTI_VIAL;
    }

    // Check for failed payment states
    if (treatmentState === 'failed') {
      return TreatmentTransferScenario.FAILED_PAYMENT;
    }

    // Check for paused states
    if (treatmentState === 'paused') {
      // Check if there's a scheduled resume date in nextEventIn
      if (
        context.nextEventIn &&
        isAfter(new Date(context.nextEventIn), new Date())
      ) {
        return TreatmentTransferScenario.PAUSED_UNTIL_DATE;
      }
      return TreatmentTransferScenario.PAUSED_INDEFINITELY;
    }

    // Check for scheduled treatments
    if (treatmentState === 'scheduled') {
      return TreatmentTransferScenario.SCHEDULED;
    }

    // Check for in-progress treatments
    const hasRemainingRefills = treatment.currentRefill < treatment.refills;
    if (hasRemainingRefills) {
      return TreatmentTransferScenario.IN_PROGRESS_WITH_REFILLS;
    } else {
      return TreatmentTransferScenario.IN_PROGRESS_NO_REFILLS;
    }
  }

  /**
   * Handles in-progress treatments with no refills left - leaves them as-is
   */
  private async handleInProgressNoRefills(): Promise<{
    success: boolean;
    message: string;
  }> {
    return {
      success: true,
      message: 'Treatment in progress with no refills, left as-is',
    };
  }

  /**
   * Handles in-progress treatments with pending refills - transfers to new pharmacy
   */
  private async handleInProgressWithRefills(
    treatment: TreatmentWithRelations,
    targetPharmacy: any,
    actor: TreatmentActor,
    context: TreatmentMachineContext,
    tx: PrismaTransactionalClient,
  ): Promise<{
    success: boolean;
    newTreatmentId?: string;
    error?: string;
    treatmentId?: string;
  }> {
    const treatmentEventsToEmit: { event: any }[] = [];

    // Re-create actor with event handler
    actor = await this.treatmentService.getActor(
      treatment.id,
      (e) => treatmentEventsToEmit.push(e),
      treatment.state as TreatmentSnapshot,
    );

    // Get the next product price ID based on current refill
    const nextRefillIndex = treatment.currentRefill + 1;
    if (nextRefillIndex >= context.products.length) {
      // Data inconsistency - log and return failure but don't block patient transfer
      this.logger.warn(
        `Treatment ${treatment.id} has no more products in context (refill ${nextRefillIndex}/${context.products.length})`,
      );
      return {
        success: false,
        error: 'No more products in context',
        treatmentId: treatment.id,
      };
    }

    const nextProductPrice = context.products[nextRefillIndex];
    if (!nextProductPrice || !nextProductPrice.id) {
      this.logger.warn(
        `Treatment ${treatment.id} has invalid next product price at index ${nextRefillIndex}`,
      );
      return {
        success: false,
        error: 'Invalid next product price',
        treatmentId: treatment.id,
      };
    }

    const nextProductPriceId = nextProductPrice.id;
    const delayUntil = context.nextRefillDate;

    if (!delayUntil) {
      // Missing date - log and return failure but don't block patient transfer
      this.logger.warn(
        `Treatment ${treatment.id} has no valid date for nextRefillDate`,
      );
      return {
        success: false,
        error: 'Missing nextRefillDate',
        treatmentId: treatment.id,
      };
    }

    // Find equivalent product in new pharmacy
    const equivalentProductResult = await this.findEquivalentProduct(
      nextProductPriceId,
      targetPharmacy.id,
      tx,
      treatment.topProductPriceId,
    );

    if (!equivalentProductResult) {
      // NO_EQUIVALENT_PRODUCT scenario - handle dynamically
      return this.handleNoEquivalentProduct(
        treatment,
        targetPharmacy,
        actor,
        tx,
      );
    }

    const {
      initialProductPriceId: newInitialProductPriceId,
      topProductPriceId: newTopProductPriceId,
    } = equivalentProductResult;

    // Calculate remaining refills
    const remainingRefills = treatment.refills - treatment.currentRefill - 1;

    // Create new treatment
    const newTreatmentData = await this.treatmentService.createTreatment(
      {
        patientId: treatment.patientId,
        doctorId: treatment.doctorId,
        pharmacyName: targetPharmacy.name,
        refills: remainingRefills,
        initialProductPriceId: newInitialProductPriceId,
        finalProductPriceId: newTopProductPriceId,
        vials: treatment.vials,
        refillSystem: treatment.refillSystem as any,
        notes: `Transferred from another pharmacy. Original treatment ID: ${treatment.id}`,
        shortInitialPrescription: false,
        delayUntil,
        isTransfer: true,
      },
      { prisma: tx },
    );

    // Send transfer event to original treatment
    actor.send({
      type: 'transfer',
      treatmentId: newTreatmentData.treatmentId,
    });

    // Update the treatment record
    const updatedTreatment = await this.treatmentService.updateTreatmentRecord(
      actor,
      { prisma: tx },
    );

    // Emit events
    for (const { event } of treatmentEventsToEmit) {
      await this.treatmentService.emitTreatmentUpdatedEvent(
        event,
        {
          ...updatedTreatment,
          transferredTo: newTreatmentData.treatmentId,
        },
        { prisma: tx },
      );
    }

    // Update the original treatment with transferredTo reference
    await tx.treatment.update({
      where: { id: treatment.id },
      data: { transferredTo: newTreatmentData.treatmentId },
    });

    // Emit prescriptionMigrated event for successful transfer
    await this.emitPrescriptionMigratedEventForNewTreatment(
      tx,
      treatment,
      targetPharmacy.name,
      actor,
      newTreatmentData,
    );

    return {
      success: true,
      newTreatmentId: newTreatmentData.treatmentId,
    };
  }

  /**
   * Handles scheduled treatments - cancels and recreates in new pharmacy
   */
  private async handleScheduledTreatment(
    treatment: TreatmentWithRelations,
    targetPharmacy: any,
    actor: TreatmentActor,
    context: TreatmentMachineContext,
    tx: PrismaTransactionalClient,
  ): Promise<{
    success: boolean;
    newTreatmentId?: string;
    error?: string;
    treatmentId?: string;
  }> {
    const treatmentEventsToEmit: { event: any }[] = [];

    // Re-create actor with event handler
    actor = await this.treatmentService.getActor(
      treatment.id,
      (e) => treatmentEventsToEmit.push(e),
      treatment.state as TreatmentSnapshot,
    );

    // For scheduled treatments, use the first product in the list
    const initialProductPrice = context.products[0];
    if (!initialProductPrice || !initialProductPrice.id) {
      this.logger.warn(
        `Treatment ${treatment.id} has invalid initial product price`,
      );
      return {
        success: false,
        error: 'Invalid initial product price',
        treatmentId: treatment.id,
      };
    }
    const nextProductPriceId = initialProductPrice.id;

    // Use original delayUntil for scheduled treatments
    const delayUntil = context.delayUntil;
    if (!delayUntil) {
      this.logger.warn(
        `Treatment ${treatment.id} has no valid date for delayUntil`,
      );
      return {
        success: false,
        error: 'Missing delayUntil date',
        treatmentId: treatment.id,
      };
    }

    // Find equivalent product in new pharmacy
    const equivalentProductResult = await this.findEquivalentProduct(
      nextProductPriceId,
      targetPharmacy.id,
      tx,
      treatment.topProductPriceId,
    );

    if (!equivalentProductResult) {
      return this.handleNoEquivalentProduct(
        treatment,
        targetPharmacy,
        actor,
        tx,
      );
    }

    const {
      initialProductPriceId: newInitialProductPriceId,
      topProductPriceId: newTopProductPriceId,
    } = equivalentProductResult;

    // Cancel the original scheduled treatment
    actor.send({ type: 'cancel' });

    // Update the treatment record
    await this.treatmentService.updateTreatmentRecord(actor, {
      prisma: tx,
    });

    // Emit events
    for (const { event } of treatmentEventsToEmit) {
      await this.treatmentService.emitTreatmentUpdatedEvent(
        event,
        treatment.id,
        { prisma: tx },
      );
    }

    // Get original treatment state for tracking
    const originalStatus = this.treatmentService.getState(actor);
    const originalPharmacy =
      treatment.initialProductPrice?.product?.pharmacy?.name || 'Unknown';

    // Extract original product information for tracking
    const originalProductInfo = getProductForAnalytics({
      dosageLabel: treatment.initialProductPrice?.dosageLabel || '',
      form: treatment.initialProductPrice?.product?.form || '',
      label: treatment.initialProductPrice?.product?.label || '',
    });

    // Create new treatment in new pharmacy
    const newTreatmentData = await this.treatmentService.createTreatment(
      {
        patientId: treatment.patientId,
        doctorId: treatment.doctorId,
        pharmacyName: targetPharmacy.name,
        refills: treatment.refills,
        initialProductPriceId: newInitialProductPriceId,
        finalProductPriceId: newTopProductPriceId,
        vials: treatment.vials,
        refillSystem: treatment.refillSystem as any,
        notes: `Transferred scheduled treatment from another pharmacy. Original treatment ID: ${treatment.id}`,
        shortInitialPrescription: false,
        delayUntil,
        isTransfer: true,
      },
      { prisma: tx },
    );

    // Update the original treatment with transferredTo reference
    await tx.treatment.update({
      where: { id: treatment.id },
      data: { transferredTo: newTreatmentData.treatmentId },
    });

    // Emit prescriptionMigrated event for successful transfer
    await this.emitPrescriptionMigratedEventForNewTreatment(
      tx,
      treatment,
      targetPharmacy.name,
      actor,
      newTreatmentData,
    );

    return {
      success: true,
      newTreatmentId: newTreatmentData.treatmentId,
    };
  }

  /**
   * Handles paused indefinitely treatments - cancels and sends doctor note
   */
  private async handlePausedIndefinitely(
    treatment: TreatmentWithRelations,
    targetPharmacy: any,
    actor: TreatmentActor,
    tx: PrismaTransactionalClient,
  ): Promise<{ success: boolean; error?: string; treatmentId?: string }> {
    const treatmentEventsToEmit: { event: any }[] = [];

    // Re-create actor with event handler
    actor = await this.treatmentService.getActor(
      treatment.id,
      (e) => treatmentEventsToEmit.push(e),
      treatment.state as TreatmentSnapshot,
    );

    this.logger.log(`Cancelling indefinitely paused treatment ${treatment.id}`);

    // Cancel the treatment
    actor.send({ type: 'cancel' });

    // Update the treatment record
    await this.treatmentService.updateTreatmentRecord(actor, {
      prisma: tx,
    });

    // Emit events
    for (const { event } of treatmentEventsToEmit) {
      await this.treatmentService.emitTreatmentUpdatedEvent(
        event,
        treatment.id,
        { prisma: tx },
      );
    }

    // Send a doctor note to inform about the cancelled paused treatment
    await this.sendDoctorNoteForTreatmentTransfer(
      tx,
      treatment,
      targetPharmacy.name,
      true, // wasCancelled = true for paused treatments
    );

    // Emit prescriptionMigrated event for noteWritten scenario
    await this.emitPrescriptionMigratedEvent(
      tx,
      treatment,
      targetPharmacy.name,
      actor,
      'noteWritten',
    );

    return { success: true };
  }

  /**
   * Handles paused until date treatments - checks refills and handles accordingly
   */
  private async handlePausedUntilDate(
    treatment: TreatmentWithRelations,
    targetPharmacy: any,
    actor: TreatmentActor,
    context: TreatmentMachineContext,
    tx: PrismaTransactionalClient,
  ): Promise<{
    success: boolean;
    newTreatmentId?: string;
    error?: string;
    treatmentId?: string;
  }> {
    //@todo keep the paused without refills or cancel it? makes no difference

    // Check if there are remaining refills
    const hasRemainingRefills = treatment.currentRefill < treatment.refills;

    if (!hasRemainingRefills) {
      // No refills left, leave as-is
      return this.handleInProgressNoRefills();
    }

    // Has refills, transfer with adjusted delay
    // TODO: Get the pause end date from context
    // For now, using the same logic as in-progress with refills
    return this.handleInProgressWithRefills(
      treatment,
      targetPharmacy,
      actor,
      context,
      tx,
    );
  }

  /**
   * Handles failed payment treatments - cancels and recreates from current refill
   */
  private async handleFailedPayment(
    treatment: TreatmentWithRelations,
    targetPharmacy: any,
    actor: TreatmentActor,
    context: TreatmentMachineContext,
    tx: PrismaTransactionalClient,
  ): Promise<{
    success: boolean;
    newTreatmentId?: string;
    error?: string;
    treatmentId?: string;
  }> {
    //@todo immediately retry or schedule? doctor msg?

    const treatmentEventsToEmit: { event: any }[] = [];

    // Re-create actor with event handler
    actor = await this.treatmentService.getActor(
      treatment.id,
      (e) => treatmentEventsToEmit.push(e),
      treatment.state as TreatmentSnapshot,
    );

    // Cancel the failed treatment
    actor.send({ type: 'cancel' });

    // Update the treatment record
    await this.treatmentService.updateTreatmentRecord(actor, {
      prisma: tx,
    });

    // Emit events
    for (const { event } of treatmentEventsToEmit) {
      await this.treatmentService.emitTreatmentUpdatedEvent(
        event,
        treatment.id,
        { prisma: tx },
      );
    }

    // Get the current product price (where it failed)
    const currentProductIndex = treatment.currentRefill;
    if (currentProductIndex >= context.products.length) {
      this.logger.warn(
        `Treatment ${treatment.id} has invalid current product index (${currentProductIndex}/${context.products.length})`,
      );
      return {
        success: false,
        error: 'Invalid current product index',
        treatmentId: treatment.id,
      };
    }

    const currentProductPrice = context.products[currentProductIndex];
    if (!currentProductPrice || !currentProductPrice.id) {
      this.logger.warn(
        `Treatment ${treatment.id} has invalid current product price at index ${currentProductIndex}`,
      );
      return {
        success: false,
        error: 'Invalid current product price',
        treatmentId: treatment.id,
      };
    }

    // Find equivalent product in new pharmacy
    const equivalentProductResult = await this.findEquivalentProduct(
      currentProductPrice.id,
      targetPharmacy.id,
      tx,
      treatment.topProductPriceId,
    );

    if (!equivalentProductResult) {
      // Send doctor note about no equivalent
      await this.sendDoctorNoteForTreatmentTransfer(
        tx,
        treatment,
        targetPharmacy.name,
        false, // wasCancelled = false (emphasis is on noEquivalent)
        undefined,
        true, // noEquivalent = true
      );

      // Emit prescriptionMigrated event for noMatch scenario
      await this.emitPrescriptionMigratedEvent(
        tx,
        treatment,
        targetPharmacy.name,
        actor,
        'noMatch',
      );

      return { success: true };
    }

    const {
      initialProductPriceId: newInitialProductPriceId,
      topProductPriceId: newTopProductPriceId,
    } = equivalentProductResult;

    // Calculate remaining refills from current refill
    const remainingRefills = treatment.refills - treatment.currentRefill;

    // Create new treatment from current refill forward
    const newTreatmentData = await this.treatmentService.createTreatment(
      {
        patientId: treatment.patientId,
        doctorId: treatment.doctorId,
        pharmacyName: targetPharmacy.name,
        refills: remainingRefills,
        initialProductPriceId: newInitialProductPriceId,
        finalProductPriceId: newTopProductPriceId,
        vials: treatment.vials,
        refillSystem: treatment.refillSystem as any,
        notes: `Transferred from failed payment treatment. Original treatment ID: ${treatment.id}`,
        shortInitialPrescription: false,
        delayUntil: new Date().toISOString(),
        isTransfer: true,
      },
      { prisma: tx },
    );

    // Update the original treatment with transferredTo reference
    await tx.treatment.update({
      where: { id: treatment.id },
      data: { transferredTo: newTreatmentData.treatmentId },
    });

    // Emit prescriptionMigrated event for successful transfer
    await this.emitPrescriptionMigratedEventForNewTreatment(
      tx,
      treatment,
      targetPharmacy.name,
      actor,
      newTreatmentData,
    );

    return {
      success: true,
      newTreatmentId: newTreatmentData.treatmentId,
    };
  }

  /**
   * Handles multi-vial treatments - strips refills and sends doctor note
   */
  private async handleMultiVialTreatment(
    treatment: TreatmentWithRelations,
    targetPharmacy: any,
    actor: TreatmentActor,
    context: TreatmentMachineContext,
    tx: PrismaTransactionalClient,
  ): Promise<{ success: boolean; error?: string; treatmentId?: string }> {
    const treatmentEventsToEmit: { event: any }[] = [];

    // Re-create actor with event handler
    actor = await this.treatmentService.getActor(
      treatment.id,
      (e) => treatmentEventsToEmit.push(e),
      treatment.state as TreatmentSnapshot,
    );

    this.logger.log(
      `Treatment ${treatment.id} has ${context.vials} vials, stripping refills and sending doctor note`,
    );

    // Strip remaining refills by sending transfer event
    actor.send({ type: 'transfer', treatmentId: treatment.id });

    // Update the treatment record
    await this.treatmentService.updateTreatmentRecord(actor, {
      prisma: tx,
    });

    // Emit events
    for (const { event } of treatmentEventsToEmit) {
      await this.treatmentService.emitTreatmentUpdatedEvent(
        event,
        treatment.id,
        { prisma: tx },
      );
    }

    // Send doctor note about no equivalence for multi-vial treatment
    await this.sendDoctorNoteForTreatmentTransfer(
      tx,
      treatment,
      targetPharmacy.name,
      false, // wasCancelled = false
      context.vials, // vialCount for multi-vial message
    );

    // Emit prescriptionMigrated event for noteWritten scenario
    await this.emitPrescriptionMigratedEvent(
      tx,
      treatment,
      targetPharmacy.name,
      actor,
      'noteWritten',
    );

    return { success: true };
  }

  /**
   * Handles treatments with no equivalent product - cancels and sends doctor note
   */
  private async handleNoEquivalentProduct(
    treatment: TreatmentWithRelations,
    targetPharmacy: any,
    actor: TreatmentActor,
    tx: PrismaTransactionalClient,
  ): Promise<{ success: boolean; error?: string; treatmentId?: string }> {
    const treatmentEventsToEmit: { event: any }[] = [];

    // Re-create actor with event handler
    actor = await this.treatmentService.getActor(
      treatment.id,
      (e) => treatmentEventsToEmit.push(e),
      treatment.state as TreatmentSnapshot,
    );

    this.logger.log(
      `No equivalent product found for treatment ${treatment.id} - cancelling and marking as passed`,
    );

    // Cancel the treatment
    actor.send({ type: 'cancel' });

    // Update the treatment record
    await this.treatmentService.updateTreatmentRecord(actor, {
      prisma: tx,
    });

    // Emit events
    for (const { event } of treatmentEventsToEmit) {
      await this.treatmentService.emitTreatmentUpdatedEvent(
        event,
        treatment.id,
        { prisma: tx },
      );
    }

    // Send a doctor note to inform about the cancelled treatment due to no equivalent
    await this.sendDoctorNoteForTreatmentTransfer(
      tx,
      treatment,
      targetPharmacy.name,
      false, // wasCancelled = false (emphasis is on noEquivalent)
      undefined,
      true, // noEquivalent = true
    );

    // Emit prescriptionMigrated event for noMatch scenario
    await this.emitPrescriptionMigratedEvent(
      tx,
      treatment,
      targetPharmacy.name,
      actor,
      'noMatch',
    );

    return { success: true };
  }

  /**
   * Finds equivalent product in the target pharmacy
   */
  private async findEquivalentProduct(
    originalProductPriceId: string,
    targetPharmacyId: string,
    tx: PrismaTransactionalClient,
    topProductPriceId?: string | null,
  ): Promise<{
    initialProductPriceId: string;
    topProductPriceId: string | null;
  } | null> {
    // Find equivalent for initial product price
    const originalProductPrice = await tx.productPrice.findUnique({
      where: { id: originalProductPriceId },
      include: {
        equivalenceGroup: {
          include: {
            productPrices: {
              include: {
                product: true,
              },
            },
          },
        },
      },
    });

    if (!originalProductPrice?.equivalenceGroupId) {
      return null;
    }

    const equivalentProductPrice =
      originalProductPrice.equivalenceGroup.productPrices.find(
        (pp) => pp.product.pharmacyId === targetPharmacyId,
      );

    if (!equivalentProductPrice) {
      return null;
    }

    // Find equivalent for top product price if provided
    let equivalentTopProductPriceId = null;
    if (topProductPriceId) {
      const topProductPrice = await tx.productPrice.findUnique({
        where: { id: topProductPriceId },
        include: {
          equivalenceGroup: {
            include: {
              productPrices: {
                include: {
                  product: true,
                },
              },
            },
          },
        },
      });

      if (topProductPrice?.equivalenceGroupId) {
        const equivalentTopProductPrice =
          topProductPrice.equivalenceGroup.productPrices.find(
            (pp) => pp.product.pharmacyId === targetPharmacyId,
          );
        if (equivalentTopProductPrice) {
          equivalentTopProductPriceId = equivalentTopProductPrice.id;
        }
      }
    }

    return {
      initialProductPriceId: equivalentProductPrice.id,
      topProductPriceId: equivalentTopProductPriceId,
    };
  }

  /**
   * Emits prescriptionMigrated event for scenarios where no new treatment is created
   */
  private async emitPrescriptionMigratedEvent(
    tx: PrismaTransactionalClient,
    treatment: TreatmentWithRelations,
    toPharmacyName: string,
    actor: TreatmentActor,
    matchFound: 'noteWritten' | 'noMatch',
  ) {
    // Get patient info for the event
    const patient = await tx.patient.findUnique({
      where: { id: treatment.patientId },
      include: {
        user: true,
        state: true,
      },
    });

    if (!patient) {
      this.logger.warn(`Patient not found for treatment ${treatment.id}`);
      return;
    }

    // Get original treatment details
    const originalStatus = this.treatmentService.getState(actor);
    const originalPharmacy =
      treatment.initialProductPrice?.product?.pharmacy?.name || 'Unknown';

    // Extract product information from the original treatment
    const genericName =
      treatment.initialProductPrice?.product?.genericName || 'semaglutide';
    const productType = getProductTypeFromGenericName(genericName);
    const productForm =
      treatment.initialProductPrice?.product?.form || 'injectable';

    // Build product details based on what's available
    const originalProduct = {
      productName: treatment.initialProductPrice?.product?.name || 'Unknown',
      productClass: treatment.initialProductPrice?.product?.type || 'core',
      productDosage: treatment.initialProductPrice?.dosageLabel || 'Unknown',
      productType,
      productForm,
    };

    const prescriptionMigratedEvent: SegmentTrack = {
      event: segmentTrackEvents.prescriptionMigrated.name,
      userId: patient.user.id,
      properties: {
        fromPharmacy: originalPharmacy,
        toPharmacy: toPharmacyName,
        originalStatus: originalStatus,
        newStatus: matchFound === 'noteWritten' ? 'cancelled' : 'noMatch',
        originalProduct: originalProduct,
        newProduct: null, // No new product created
        matchFound,
      },
    };

    this.eventEmitter.emit(
      segmentTrackEvents.prescriptionMigrated.event,
      prescriptionMigratedEvent,
    );
  }

  /**
   * Emits prescriptionMigrated event for successful transfer with new treatment creation
   */
  private async emitPrescriptionMigratedEventForNewTreatment(
    tx: PrismaTransactionalClient,
    originalTreatment: TreatmentWithRelations,
    toPharmacyName: string,
    actor: TreatmentActor,
    newTreatmentData: any,
  ) {
    // Get patient info for the event
    const patient = await tx.patient.findUnique({
      where: { id: originalTreatment.patientId },
      include: {
        user: true,
        state: true,
      },
    });

    if (!patient) {
      this.logger.warn(
        `Patient not found for treatment ${originalTreatment.id}`,
      );
      return;
    }

    // Get original treatment details
    const originalStatus = this.treatmentService.getState(actor);
    const originalPharmacy =
      originalTreatment.initialProductPrice?.product?.pharmacy?.name ||
      'Unknown';

    // Extract original product information
    const originalProductInfo = getProductForAnalytics({
      dosageLabel: originalTreatment.initialProductPrice?.dosageLabel || '',
      form: originalTreatment.initialProductPrice?.product?.form || '',
      label: originalTreatment.initialProductPrice?.product?.label || '',
    });

    // Get new treatment product info - need to fetch the new product price details
    const newProductPrice = await tx.productPrice.findUnique({
      where: { id: newTreatmentData.products[0].id },
      include: {
        product: true,
      },
    });

    const newProductInfo = newProductPrice
      ? getProductForAnalytics({
          dosageLabel: newProductPrice.dosageLabel || '',
          form: newProductPrice.product?.form || '',
          label: newProductPrice.product?.label || '',
        })
      : originalProductInfo; // Fallback to original if new not found

    const prescriptionMigratedEvent: SegmentTrack = {
      event: segmentTrackEvents.prescriptionMigrated.name,
      userId: patient.user.id,
      properties: {
        fromPharmacy: originalPharmacy,
        toPharmacy: toPharmacyName,
        originalStatus: originalStatus,
        newStatus: 'scheduled',
        originalProduct: originalProductInfo,
        newProduct: newProductInfo,
        matchFound: 'prescriptionCreated',
      },
    };

    this.eventEmitter.emit(
      segmentTrackEvents.prescriptionMigrated.event,
      prescriptionMigratedEvent,
    );
  }
}
