export const segmentTrackEvents = {
  /**
   * Marketing: User Submits Email.
   */
  chargeRefunded: {
    event: 'track.charge.refunded',
    name: 'RefundIssued',
  },
  /**
   * Marketing: User Submits Email.
   */
  leadGenerated: {
    event: 'track.lead.generated',
    name: 'LeadGenerated',
  },
  /**
   * Onboarding: Onboarding Step 1 Load.
   */
  onbardingStarted: {
    event: 'track.onboarding.started',
    name: 'OnboardingStarted',
  },

  /**
   * Onboarding: User Enters State.
   */
  stateSelected: { event: 'track.state.selected', name: 'StateSelected' },
  /**
   * Onboarding: User is Rejected from Onboarding.
   */
  onboardingRejected: {
    event: 'track.onboarding.rejected',
    name: 'OnboardingReject',
  },
  onboardingCompleted: {
    event: 'track.onboarding.completed',
    name: 'OnboardingComplete',
  },
  /**
   *Onboarding: User Joins State Waitlist.
   */
  waitlistJoined: {
    event: 'track.waitlist.joined',
    name: 'WaitlistJoined',
  },
  /**
   * Onboarding: User successfully enters necessary info and creates their account.
   */
  accountCreated: {
    event: 'track.account.created',
    name: 'AccountCreated',
  },
  signUp: {
    event: 'track.signUp',
    name: 'SignUp',
  },
  /**
   * Onboarding: User completes the Account Creation segment of onboarding now starting their "Virual Visit".
   * They click continue here
   */
  visitStarted: {
    event: 'track.visit.started',
    name: 'VisitStarted',
  },

  /**
   * Onboarding: User completes the medical questionnaire section of onboarding, now moving to treatment selection
   */
  questionnaireCompleted: {
    event: 'track.questionnaire.completed',
    name: 'QuestionnaireCompleted',
  },

  /**
   * Onboarding: User completes the necessity statement/objectives section of the questionnaire
   */
  necessityStatementComplete: {
    event: 'track.necessity.statement.complete',
    name: 'necessityStatementComplete',
  },

  /**
   * Onboarding: User Selects their treatment choice, including if they choose to have the doctor choose for them and clicks continue
   */
  treatmentChosen: {
    event: 'track.treatment.chosen',
    name: 'TreatmentChosen',
  },
  /**
   * Onboarding: User clicks continue starting the Identity verification section
   */
  identityVerificationStarted: {
    event: 'track.identity.verification.started',
    name: 'IdentityVerificationStarted',
  },

  /**
   * Onboarding:  User uploads their personal photo
   */
  photoUploaded: {
    event: 'track.photo.uploaded',
    name: 'PhotoUploaded',
  },
  /**
   * Onboarding: User skips uploading their personal photo
   */
  photoUploadSkipped: {
    event: 'track.photo.uploaded.skipped',
    name: 'PhotoUploadSkipped',
  },
  /**
   * Onboarding: User uploads their ID
   */
  iDUploaded: {
    event: 'track.id.uploaded',
    name: 'IDUploaded',
  },
  /**
   * Onboarding: User skips uploading their ID
   */
  iDUploadSkipped: {
    event: 'track.id.uploaded.skipped',
    name: 'IDUploadSkipped',
  },

  /**
   * Onboarding: User completes identity verification clicking continue here
   */
  identityVerificationComplete: {
    event: 'track.identity.verification.complete',
    name: 'IdentityVerificationComplete',
  },
  /**
   * Onboarding: User completes viewing their visit summary, clicking continue here
   */
  visitSummary: {
    event: 'track.visit.summary',
    name: 'VisitSummary',
  },
  /**
   * Onboarding: User enters their shipping info successfully and clicks continue
   */
  checkoutStarted: {
    event: 'track.checkout.started',
    name: 'CheckoutStarted',
  },
  /**
   * Onboarding: User successfully clicks submit and checks out
   */
  checkoutComplete: {
    event: 'track.checkout.complete',
    name: 'CheckoutComplete',
  },
  /**
   * Patient Intake: Doctor within Doctor Dash Accepts patient
   */
  patientAccepted: {
    event: 'track.patient.accepted',
    name: 'PatientAccepted',
  },
  /**
   * Patient Intake: Doctor opens the Patient Identity Verification Page within Doctor Dash
   */
  patientIdentityVerificationStarted: {
    event: 'track.patient.identity.verification.started',
    name: 'PatientIdentityVerificationStarted',
  },
  /**
   * Patient Intake: Doctor accepts/verifies patient identity within doctor dash
   */
  identityAccepted: {
    event: 'track.identity.accepted',
    name: 'IdentityAccepted',
  },
  /**
   * Patient Intake: Doctor rejects patient identity within doctor dash copy
   */
  identityRejected: {
    event: 'track.identity.rejected',
    name: 'IdentityRejected',
  },
  /**
   * Patient Intake:
   */
  medicationPrescribed: {
    event: 'track.medication.prescribed',
    name: 'MedicationPrescribed',
  },
  /**
   * Patient: patient subscription is canceled
   */
  patientCancelled: {
    event: 'track.account.cancelled',
    name: 'patientCancelled',
  },
  /**
   * Patient: patient subscription is uncancelled
   */
  patientUncancelled: {
    event: 'track.account.uncancelled',
    name: 'patientUncancelled',
  },
  /**
   * Operations:
   */
  doctorSentMessage: {
    event: 'track.doctor.sent.message',
    name: 'DoctorSentMessage',
  },
  /**
   * Operations:
   */
  patientSentMessage: {
    event: 'track.patient.sent.message',
    name: 'PatientSentMessage',
  },
  /**
   * Operations: Patient updates their billing formation and clicks save
   */
  billingUpdated: {
    event: 'track.billing.updated',
    name: 'BillingUpdated',
  },
  /**
   * Operations: Patient updates their shipping info
   */
  shippingUpdated: {
    event: 'track.shipping.updated',
    name: 'ShippingUpdated',
  },
  /**
   * Operations: Patient updates their shipping info
   */
  profileUpdated: {
    event: 'track.profile.updated',
    name: 'ProfileUpdated',
  },
  emailUpdated: {
    event: 'track.profile.email.updated',
    name: 'EmailUpdated',
  },
  /**
   * Operations: Stripe charge succesful
   */
  paymentProcess: {
    event: 'track.payment.process',
    name: 'paymentProcessed',
  },
  productPurchase: {
    event: 'track.product.purchase',
    name: 'productPurchase',
  },
  /**
   * Operations: Stripe charge unsuccessful
   */
  paymentFailed: {
    event: 'track.payment.failed',
    name: 'paymentFailed',
  },
  /**
   * Operations: Stripe charge unsuccessful
   */
  dosageNotification: {
    event: 'track.treatment.dosage.notification',
    name: 'dosageNotification',
  },
  newRefillSoon: {
    event: 'track.treatment.refillNotification',
    name: 'refillNotification',
  },
  subscriptionCancelled: {
    event: 'track.treatment.cancelled',
    name: 'subscriptionCancelled',
  },
  /**
   * Operations: Stripe invoice uncollectible
   */
  invoiceUncollectible: {
    event: 'track.payment.uncollectible',
    name: 'invoiceUncollectible',
  },

  patientDeleted: {
    event: 'track.patient.deleted',
    name: 'patientDeleted',
  },
  treatmentTypeChosen: {
    event: 'track.treatmentType.chosen',
    name: 'treatmentTypeChosen',
  },

  followUpSent: {
    event: 'track.followup.sent',
    name: 'followUpSent',
  },
  followUpStarted: {
    event: 'track.followup.started',
    name: 'followUpStarted',
  },
  followUpComplete: {
    event: 'track.followup.completed',
    name: 'followUpComplete',
  },
  followUpCancelled: {
    event: 'track.followup.cancelled',
    name: 'followUpCancelled',
  },
  doctorCompleteFollowUp: {
    event: 'track.followup.doctorCompleted',
    name: 'doctorCompleteFollowUp',
  },
  followUpNpsSurveyResponse: {
    event: 'track.followup.npsSurveyResponse',
    name: 'NPS Survey Response',
  },
  patientReassigned: {
    event: 'track.patient.reassigned',
    name: 'patientReassigned',
  },
  pharmacyMigrated: {
    event: 'track.pharmacy.migrated',
    name: 'PharmacyMigrated',
  },
  orderSent: {
    event: 'track.order.sent',
    name: 'orderSent',
  },

  referralSent: {
    event: 'track.referral.sent',
    name: 'referralSent',
  },

  referralLinkAccountCreated: {
    event: 'track.referral.accountCreated',
    name: 'referralLinkAccountCreated',
  },

  referralReceived: {
    event: 'track.referral.received',
    name: 'referralReceived',
  },

  referralLinkCheckoutComplete: {
    event: 'track.referral.referralCheckoutComplete',
    name: 'referralLinkCheckoutComplete',
  },
  disputeFiled: {
    event: 'track.dispute.filed',
    name: 'disputeFiled',
  },
  messageRouted: {
    event: 'track.message.routed',
    name: 'MessageRouted',
  },
  prescriptionTransferred: {
    event: 'track.prescription.transferred',
    name: 'PrescriptionTransferred',
  },
  prescriptionMigrated: {
    event: 'track.prescription.migrated',
    name: 'PrescriptionMigrated',
  },
  prescriptionUncollectible: {
    event: 'track.prescription.uncollectible',
    name: 'prescriptionUncollectible',
  },
  adminMessagedDoctor: {
    event: 'track.admin.messaged.doctor',
    name: 'AdminMessagedDoctor',
  },
  doctorMessagedAdmin: {
    event: 'track.doctor.messaged.admin',
    name: 'DoctorMessagedAdmin',
  },
  verificationServiceSubmit: {
    event: 'track.verification.service.submit',
    name: 'VerificationServiceSubmit',
  },
  verificationServiceAccept: {
    event: 'track.verification.service.accept',
    name: 'VerificationServiceAccept',
  },
  verificationServiceReject: {
    event: 'track.verification.service.reject',
    name: 'VerificationServiceReject',
  },
} as const;

export const segmentIdentifyEvent = {
  /**
   * All identify event for a particulare source, For now goes to default source.
   */
  analyticIdentify: 'segment.identify',
} as const;
