import { runInDbTransaction } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { TreatmentUpdatedEvent } from '@/modules/shared/events/treatment-topic.definition';
import { OutboxerService } from '@/modules/shared/outboxer/outboxer.service';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@modules/prisma/prisma.service';
import { segmentTrackEvents } from '@modules/shared/events';
import {
  getProductForAnalytics,
  titleCase,
} from '@modules/shared/helpers/generic';
import { SegmentTrack } from '@modules/shared/types/events';
import {
  ActiveTreatmentProduct,
  createTreatmentMachine,
  RefillSystem,
  TreatmentProduct,
} from '@modules/treatment/states/treatment.state';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Product, ProductPrice, Treatment } from '@prisma/client';
import * as dayjs from 'dayjs';
import { v4 as uuid } from 'uuid';
import {
  ActorRefFrom,
  createActor,
  EventFromLogic,
  SnapshotFrom,
  StateValueFrom,
} from 'xstate';

type TreatmentMachine = ReturnType<typeof createTreatmentMachine>;
export type TreatmentActor = ActorRefFrom<TreatmentMachine>;
export type TreatmentSnapshot = SnapshotFrom<TreatmentMachine>;
export type TreatmentEvent = EventFromLogic<TreatmentMachine>;
export type TreatmentStateValue = StateValueFrom<TreatmentMachine>;

export type TreatmentData = {
  treatmentId: string;
  activeProduct: ActiveTreatmentProduct;
  isCore: boolean;
  currentRefill: number;
  refills: number;
  vials: number;
  refillSystem: RefillSystem;
  nextEventIn: string | null;
  nextRefillDate: string | null;
  endOfLastRefillDate: string | null;
  inProgressSince: string | null;
  lastChargedAt: string | null;
  failedAt: string | null;
  completedAt: string | null;
  cancelledAt: string | null;
  uncollectibleAt: string | null;
  deletedAt: string | null;
  state: string;
  pharmacy: string;
  products: TreatmentProduct[];
  allowedActions: Array<TreatmentEvent['type']>;
  transferredTo: string | null;
};

type CreateTreatmentInput = {
  patientId: string;
  doctorId: string;
  pharmacyName: string;
  refills: number;
  couponCode?: string;
  delayUntil?: string;
  initialProductPriceId: string;
  finalProductPriceId?: string;
  vials: number;
  refillSystem: RefillSystem;
  notes?: string;
  shortInitialPrescription?: boolean;
  isTransfer?: boolean;
};

type ExecutionContext = { prisma?: PrismaTransactionalClient };

@Injectable()
export class TreatmentService {
  constructor(
    private readonly outboxer: OutboxerService,
    private readonly eventEmitter: EventEmitter2,
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
  ) {}

  async getActor(
    treatmentId: string,
    emitEvent: (event: { event: TreatmentUpdatedEvent['event'] }) => void,
    snapshot?: TreatmentSnapshot,
    ctx: ExecutionContext = { prisma: this.prisma },
  ): Promise<TreatmentActor> {
    if (!snapshot) {
      const treatment = await ctx.prisma.treatment.findUnique({
        where: { id: treatmentId },
        select: { state: true },
      });
      if (!treatment) {
        throw new NotFoundException(
          `Treatment with id ${treatmentId} not found`,
        );
      }
      snapshot = treatment.state as unknown as TreatmentSnapshot;
    }

    try {
      const machine = createTreatmentMachine(emitEvent);
      const actor = createActor(machine, {
        snapshot: this.recoverSnapshot(snapshot),
      });
      actor.start();
      return actor;
    } catch (error) {
      console.error(
        `Error creating or starting actor for treatment ${treatmentId}:`,
        error,
      );
    }
  }

  getTreatment(actor: TreatmentActor): TreatmentData {
    const snapshot = actor.getSnapshot();
    const {
      treatmentId,
      products,
      activeProduct,
      isCore,
      pharmacy,
      currentRefill,
      refills,
      vials,
      refillSystem,
      nextEventIn,
      inProgressSince,
      lastChargedAt,
      pausedAt,
      failedAt,
      completedAt,
      cancelledAt,
      uncollectibleAt,
      deletedAt,
      transferredTo,
    } = snapshot.context;

    /**
     * Paused state is tricky, it's better to calculate the diff and apply it when the treatment is un-paused,
     * but for UI purposes, we need to show the paused dates unchanged, so it's better to do it here
     * than in the state machine.
     */
    const state = this.getState(actor);
    let nextRefillDate = snapshot.context.nextRefillDate;
    let endOfLastRefillDate = snapshot.context.endOfLastRefillDate;
    if (state === 'paused') {
      const diff = dayjs().diff(dayjs(pausedAt));
      nextRefillDate = dayjs(nextRefillDate).add(diff).toISOString();
      endOfLastRefillDate = dayjs(endOfLastRefillDate).add(diff).toISOString();
    }

    return {
      treatmentId,
      pharmacy,
      activeProduct,
      isCore,
      currentRefill,
      refills,
      vials,
      refillSystem,
      nextEventIn,
      nextRefillDate,
      endOfLastRefillDate,
      inProgressSince,
      lastChargedAt,
      failedAt,
      completedAt,
      cancelledAt,
      uncollectibleAt,
      deletedAt,
      state,
      products,
      allowedActions: this.getAllowedEvents(actor),
      transferredTo,
    };
  }

  getState(actor: TreatmentActor): string {
    return this.flattenState(actor.getSnapshot().value);
  }

  async updateTreatmentRecord(
    actor: TreatmentActor,
    ctx: ExecutionContext = { prisma: this.prisma },
  ) {
    return runInDbTransaction(ctx.prisma, async (prisma) => {
      const snapshot = actor.getSnapshot();
      const treatmentId = snapshot.context.treatmentId;
      const persistedSnapshot = this.recoverSnapshot(
        actor.getPersistedSnapshot() as TreatmentSnapshot,
      );

      const {
        currentRefill,
        nextEventIn,
        nextNotificationIn,
        completedAt,
        failedAt,
        cancelledAt,
        uncollectibleAt,
        deletedAt,
      } = snapshot.context;

      return prisma.treatment.update({
        where: { id: treatmentId },
        data: {
          state: persistedSnapshot,
          status: this.getState(actor),
          currentRefill,
          nextEventIn: nextEventIn ? new Date(nextEventIn) : null,
          nextNotificationIn: nextNotificationIn
            ? new Date(nextNotificationIn)
            : null,
          completedAt: completedAt ? new Date(completedAt) : null,
          failedAt: failedAt ? new Date(failedAt) : null,
          cancelledAt: cancelledAt ? new Date(cancelledAt) : null,
          uncollectibleAt: uncollectibleAt ? new Date(uncollectibleAt) : null,
          deletedAt: deletedAt ? new Date(deletedAt) : null,
          updatedAt: new Date(),
        },
      });
    });
  }

  async createTreatment(
    input: CreateTreatmentInput,
    ctx: ExecutionContext = { prisma: this.prisma },
  ) {
    return runInDbTransaction(ctx.prisma, async (prisma) => {
      const initialProductPrice = await prisma.productPrice.findUnique({
        where: { id: input.initialProductPriceId },
        include: { product: true },
      });

      if (!initialProductPrice) {
        throw new NotFoundException('Unable to find the initial Product Price');
      }

      if (input.vials > 1) {
        if (input.refillSystem !== 'static') {
          throw new BadRequestException(
            'Cannot have multiple vials with scaling or downscaling refill system',
          );
        }
        if (input.shortInitialPrescription) {
          throw new BadRequestException(
            'Cannot have multiple vials with short initial prescription',
          );
        }
      }

      const productPricesList = await this.getProductPricesList(input);

      const isCore = initialProductPrice.product.metadata['type'] === 'core';

      const treatmentEventsToEmit: {
        event: TreatmentUpdatedEvent['event'];
      }[] = [];
      const machine = createTreatmentMachine((e) => {
        console.log(`TreatmentService->createTreatment event in treatment:`, e);
        treatmentEventsToEmit.push(e);
      });
      const actor = createActor(machine, {
        input: {
          patientId: input.patientId,
          treatmentId: uuid(),
          pharmacy: input.pharmacyName,
          isCore,
          refills: input.refills,
          refillSystem: input.refillSystem,
          vials: input.vials,
          delayUntil: input.delayUntil,
          productPricesList,
        },
      });

      // Start the actor to init any initial transitions that might be needed
      actor.start();

      const context = actor.getSnapshot().context;

      const treatment = await prisma.treatment.create({
        data: {
          id: context.treatmentId,
          refills: input.refills,
          patientId: input.patientId,
          doctorId: input.doctorId,
          initialProductPriceId: input.initialProductPriceId,
          topProductPriceId: input.finalProductPriceId,
          couponCode: input.couponCode,
          isCore,
          refillSystem: input.refillSystem,
          vials: input.vials,
          notes: input.notes,
          state: actor.getPersistedSnapshot() as TreatmentSnapshot,
          status: this.getState(actor),
          currentRefill: context.currentRefill,
          nextEventIn: context.nextEventIn,
          completedAt: context.completedAt,
          failedAt: context.failedAt,
          cancelledAt: context.cancelledAt,
          uncollectibleAt: context.uncollectibleAt,
        },
        include: {
          patient: {
            select: { user: { select: { firstName: true, lastName: true } } },
          },
          doctor: {
            select: { user: { select: { firstName: true, lastName: true } } },
          },
        },
      });

      await this.auditService.append(
        {
          patientId: treatment.patientId,
          action: 'TREATMENT_CREATED',
          actorType: 'DOCTOR',
          actorId: treatment.doctorId,
          resourceType: 'TREATMENT',
          resourceId: context.treatmentId,
          details: {
            doctorId: treatment.doctorId,
            doctorName: `${treatment.doctor.user.firstName} ${treatment.doctor.user.lastName}`,
            patientId: treatment.patientId,
            patientFirstName: treatment.patient.user.firstName,
            patientLastName: treatment.patient.user.lastName,
            pharmacyName: input.pharmacyName,
            productName: `${initialProductPrice.metadata['form']} ${initialProductPrice.product.metadata['label']} - ${initialProductPrice.metadata['dosageLabel']} (${input.pharmacyName})`,
            refills: treatment.refills,
            refillSystem: input.refillSystem,
            couponCode: input.couponCode,
            delayUntil: input.delayUntil,
            initialProductPriceId: input.initialProductPriceId,
            finalProductPriceId: input.finalProductPriceId,
            vials: treatment.vials,
            notes: treatment.notes,
            shortInitialPrescription: input.shortInitialPrescription,
          },
        },
        { prisma },
      );

      // segment tracking
      if (isCore) {
        const treatmentCount = await prisma.treatment.count({
          where: { patientId: input.patientId, isCore: true },
        });
        const patient = await prisma.patient.findUniqueOrThrow({
          where: { id: input.patientId },
          include: {
            state: true,
            doctor: {
              include: { user: true },
            },
            prescriptions: true,
          },
        });

        const {
          productName,
          productClass,
          productType,
          productForm,
          productDosage,
        } = getProductForAnalytics({
          dosageLabel: initialProductPrice.dosageLabel,
          form: initialProductPrice.product.form,
          label: initialProductPrice.product.label,
        });
        // we need to send the form in title case
        const form = titleCase(initialProductPrice.product.form);
        const doctorName = `${patient.doctor.user.firstName} ${patient.doctor.user.lastName}`;

        if (!input.isTransfer) {
          // Emit regular medicationPrescribed event for non-transfers
          const medicationPrescribedEvent: SegmentTrack = {
            event: segmentTrackEvents.medicationPrescribed.name,
            userId: input.patientId,
            properties: {
              state: patient.state.code,
              doctorId: input.doctorId,
              doctorName: doctorName,
              value: (context.products[0].price / 100).toFixed(2),
              form,
              firstPrescribe: treatmentCount === 1,
              billingCycle: `${context.vials} Month`,
              productName,
              productClass,
              productType,
              productForm,
              productDosage,
            },
          };
          this.eventEmitter.emit(
            segmentTrackEvents.medicationPrescribed.event,
            medicationPrescribedEvent,
          );
        }
      }

      await this.outboxer.enqueue(
        treatment.patientId,
        'treatment-updated',
        { event: 'created', treatment },
        { prisma },
      );
      for (const event of treatmentEventsToEmit) {
        await this.outboxer.enqueue(
          treatment.patientId,
          'treatment-updated',
          { event: event.event, treatment },
          { prisma },
        );
      }

      return this.getTreatment(actor);
    });
  }

  getAllowedEvents(actor: TreatmentActor): Array<TreatmentEvent['type']> {
    const snapshot = actor.getSnapshot();
    const allEvents = this.getAllPossibleEvents();
    return allEvents.filter((eventType) =>
      snapshot.can({ type: eventType } as TreatmentEvent),
    );
  }

  async isCore(
    treatmentId: string,
    { prisma }: ExecutionContext = { prisma: this.prisma },
  ) {
    const treatment = await prisma.treatment.findFirst({
      where: { id: treatmentId },
    });
    return treatment ? treatment.isCore : false;
  }

  private getAllPossibleEvents(): Array<TreatmentEvent['type']> {
    const events = new Set<string>();
    const addEventsFromNode = (node: any) => {
      if (node.on) {
        Object.keys(node.on).forEach((event) => events.add(event));
      }
      if (node.states) {
        Object.values(node.states).forEach(addEventsFromNode);
      }
    };
    addEventsFromNode(
      createTreatmentMachine((e) => {
        console.log(
          `TreatmentService->getAllPossibleEvents event in treatment:`,
          e,
        );
      }),
    );
    return Array.from(events) as Array<TreatmentEvent['type']>;
  }

  private recoverSnapshot(snapshot: TreatmentSnapshot): TreatmentSnapshot {
    if (snapshot.status === 'error') {
      console.warn(
        `Recovering snapshot for treatment ${snapshot.context.treatmentId}`,
      );
      return {
        ...snapshot,
        status: 'active',
        error: undefined,
      } as TreatmentSnapshot;
    }
    return snapshot;
  }

  private flattenState(state: TreatmentStateValue): string {
    if (typeof state === 'string') {
      return state;
    }
    const entries = Object.entries(state);
    if (entries.length === 0) {
      return '';
    }
    const [key, value] = entries[0];
    if (typeof value === 'string') {
      return `${key}.${value}`;
    }
    return `${key}.${this.flattenState(value)}`;
  }

  /**
   * Generate the list of product prices that will be used for the treatment
   *
   * @param input - The input data for creating a treatment
   * @returns A Promise resolving to an array of TreatmentProduct objects
   * @private
   *
   * This method generates a list of product prices for a treatment based on the initial product price and the input parameters.
   * It handles two types of refill systems:
   *
   * 1. Static: Repeats the initial product price for all refills.
   * 2. Scaling: Builds a progression of product prices based on the 'phase' metadata.
   *
   * Finally, it maps the product prices to TreatmentProduct objects
   */
  public async getProductPricesList(
    input: CreateTreatmentInput,
    ctx: ExecutionContext = { prisma: this.prisma },
  ): Promise<TreatmentProduct[]> {
    type ProductPriceWithProduct = ProductPrice & { product: Product };

    const initialProductPrice = await ctx.prisma.productPrice.findUnique({
      where: { id: input.initialProductPriceId },
      include: { product: true },
    });

    if (!initialProductPrice) {
      throw new NotFoundException('Unable to find the initial Product Price');
    }

    const productPricesList: ProductPriceWithProduct[] = [initialProductPrice];

    if (input.refillSystem === 'static') {
      productPricesList.push(...Array(input.refills).fill(initialProductPrice));
    } else if (['scaling', 'downscaling'].includes(input.refillSystem)) {
      let allProductPrices = await ctx.prisma.productPrice.findMany({
        where: {
          productId: initialProductPrice.productId,
          active: true,
        },
        include: {
          product: true,
        },
      });

      // Sort based on phase
      allProductPrices.sort((a, b) => {
        const phaseA = parseInt(a.metadata['phase'] as string, 10);
        const phaseB = parseInt(b.metadata['phase'] as string, 10);
        return phaseA - phaseB;
      });

      let currentPhase = parseInt(
        initialProductPrice.metadata['phase'] as string,
        10,
      );

      if (input.finalProductPriceId) {
        const finalProduct = allProductPrices.find(
          (price) => price.id === input.finalProductPriceId,
        );
        if (finalProduct) {
          const finalPhase = parseInt(
            finalProduct.metadata['phase'] as string,
            10,
          );
          // Filter products to only include those between initial and final phases
          allProductPrices = allProductPrices.filter((price) => {
            const phase = parseInt(price.metadata['phase'] as string, 10);
            return input.refillSystem === 'scaling'
              ? phase >= currentPhase && phase <= finalPhase
              : phase <= currentPhase && phase >= finalPhase;
          });
        }
      }

      // For downscaling, reverse the array after filtering
      if (input.refillSystem === 'downscaling') {
        allProductPrices.reverse();
      }

      for (let i = 0; i < input.refills; i++) {
        const nextProductPrice =
          allProductPrices.find((price) => {
            const phase = parseInt(price.metadata['phase'] as string, 10);
            return input.refillSystem === 'scaling'
              ? phase > currentPhase
              : phase < currentPhase;
          }) || allProductPrices[allProductPrices.length - 1];

        productPricesList.push(nextProductPrice);
        currentPhase = parseInt(
          nextProductPrice.metadata['phase'] as string,
          10,
        );
      }
    }

    return productPricesList.map((productPrice, index) => ({
      id: productPrice.id,
      price: productPrice.unit_amount,
      name: productPrice.product.metadata['label'],
      days:
        (input.shortInitialPrescription && index === 0 ? 21 : 28) * input.vials,
      dose: productPrice.metadata['milligrams'] as string,
      dosageLabel: productPrice.dosageLabel,
      form: productPrice.product.form,
      pharmacy: input.pharmacyName,
    }));
  }

  async getActiveTreatmentByPatientId(
    patientId: string,
    isCore = true,
    ctx: ExecutionContext = { prisma: this.prisma },
  ) {
    return ctx.prisma.treatment.findFirst({
      where: {
        AND: [
          {
            OR: [
              { status: { startsWith: 'inProgress' } },
              { status: 'paused' },
            ],
          },
        ],
        patientId,
        isCore,
        deletedAt: null,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async retryPayment(treatmentId: string, ctx: ExecutionContext = {}) {
    return runInDbTransaction(ctx.prisma, async (prisma) => {
      const treatment = await prisma.treatment.findFirstOrThrow({
        where: { id: treatmentId },
        include: { prescription: true },
      });

      const treatmentEventsToEmit: {
        event: TreatmentUpdatedEvent['event'];
      }[] = [];
      const actor = await this.getActor(
        treatmentId,
        (e) => {
          treatmentEventsToEmit.push(e);
          console.log('[retryPayment] emitTreatmentEvent', e);
        },
        treatment.state as unknown as TreatmentSnapshot,
      );
      const invoiceId = treatment.prescription
        .filter((p) => p.status === 'failed')
        .sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
        )[0]?.stripeInvoiceId;

      if (!invoiceId) {
        console.error(`No failed invoices found for treatment ${treatmentId}`);
        return;
      }

      if (!actor.getSnapshot().can({ type: 'retry', invoiceId })) {
        console.error(`Cannot retry payment for treatment ${treatmentId}`);
        return;
      }

      actor.send({ type: 'retry', invoiceId });

      const updatedTreatment = await this.updateTreatmentRecord(actor, {
        prisma,
      });
      for (const { event } of treatmentEventsToEmit) {
        await this.emitTreatmentUpdatedEvent(event, updatedTreatment, {
          prisma,
        });
      }
      return this.getTreatment(actor);
    });
  }

  async emitTreatmentUpdatedEvent(
    event: TreatmentUpdatedEvent['event'],
    treatment: string | Treatment,
    ctx: ExecutionContext = {},
  ) {
    const prisma = ctx.prisma ?? this.prisma;
    let _treatment: Treatment = treatment as Treatment;
    if (typeof treatment === 'string') {
      _treatment = await prisma.treatment.findFirstOrThrow({
        where: { id: treatment },
      });
    }

    let deduplication: {
      deduplicationId?: string;
      deduplicationPeriodInSeconds?: number;
    } = {};

    if (
      [
        'prescription_create',
        'prescription_prescribe',
        'treatment_delete',
        'treatment_notify_nextDose',
        'treatment_notify_nextRefill',
        'treatment_notify_transferRefill',
      ].includes(event)
    ) {
      deduplication = {
        deduplicationId: `${_treatment.id}-${event}`,
        deduplicationPeriodInSeconds: 60, // 1 minute
      };
    }

    return this.outboxer.enqueue(
      _treatment.patientId,
      'treatment-updated',
      {
        event,
        treatment: _treatment,
      },
      { prisma: ctx.prisma, ...deduplication },
    );
  }
}
