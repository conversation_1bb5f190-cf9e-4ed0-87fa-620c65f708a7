import { PatientService } from '@/modules/patient/patient.service';
import { segmentIdentifyEvent } from '@/modules/shared/events';
import { SegmentIdentify } from '@/modules/shared/types/events';
import { PrismaService } from '@modules/prisma/prisma.service';
import { CreateTreatmentArrayDTO } from '@modules/treatment/dto/create-treatment.dto';
import {
  TreatmentData,
  TreatmentService,
} from '@modules/treatment/services/treatment.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  Doctor,
  Patient,
  patientStatus,
  prescriptionStatus,
  Prisma,
} from '@prisma/client';

type PatientWithPrescriptions = Prisma.PatientGetPayload<{
  include: { prescriptions: true };
}>;

@Injectable()
export class TreatmentCreateUseCase {
  constructor(
    private readonly treatmentService: TreatmentService,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly patientService: PatientService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async execute(
    doctorId: string,
    {
      patientId,
      pharmacyId,
      applyDiscount,
      prescription,
    }: CreateTreatmentArrayDTO,
  ): Promise<TreatmentData[]> {
    const patient = await this.prisma.patient.findUniqueOrThrow({
      where: { id: patientId },
      include: { doctor: true, prescriptions: true },
    });

    if (!patient.doctor) {
      throw new BadRequestException('Patient does not have an assigned doctor');
    }
    const { name: pharmacyName } = await this.prisma.pharmacy.findUniqueOrThrow(
      { where: { id: pharmacyId, enabled: true } },
    );

    const hasManyVials = prescription.some((t) => t.vials > 1);
    if (applyDiscount && hasManyVials) {
      throw new BadRequestException(
        'Cannot apply Willow discount with multiple vials',
      );
    }

    this.validatePatientStatus(patient);
    await this.updatePatient(patient, pharmacyId, pharmacyName);
    void this.identifyNewPatientAsActive(patient);
    await this.reActivatePatientIfCancelled(patient, patient.doctor);

    // Simplified - only pass Willow discount if requested
    const couponCode = applyDiscount
      ? this.configService.get<string>('STRIPE_WILLOW_DISCOUNT_CODE')
      : null;

    const treatmentsCreated: TreatmentData[] = [];

    for (const treatmentToCreate of prescription) {
      try {
        const treatment = await this.treatmentService.createTreatment({
          patientId,
          doctorId,
          pharmacyName,
          refills: treatmentToCreate.refills,
          couponCode,
          delayUntil: treatmentToCreate.delayUntil,
          initialProductPriceId: treatmentToCreate.initialProductPriceId,
          finalProductPriceId: treatmentToCreate.finalProductPriceId,
          refillSystem: treatmentToCreate.refillSystem,
          vials: treatmentToCreate.vials,
          notes: treatmentToCreate.notes,
          shortInitialPrescription: treatmentToCreate.shortInitialPrescription,
          isTransfer: false,
        });
        treatmentsCreated.push(treatment);
      } catch (error) {
        console.error(`Error creating treatment: ${error.message}`);
      }
    }

    return treatmentsCreated;
  }

  private validatePatientStatus(patient: Patient) {
    const validStatuses = [
      'activePrescription',
      'pendingPrescription',
      'nonActivePrescription',
      'cancelled',
    ];
    if (patient.verificationStatus === 'verified') {
      validStatuses.push('pendingApprovalFromDoctor');
    }

    if (!validStatuses.includes(patient.status)) {
      throw new BadRequestException(
        'Patient status is not valid for creating a treatment',
      );
    }
  }

  private async updatePatient(
    patient: PatientWithPrescriptions,
    pharmacyId: string,
    pharmacyName: string,
  ) {
    const finalStates: prescriptionStatus[] = [
      'paid',
      'failed',
      'uncollectible',
    ];
    const hasActivePrescription = patient.prescriptions.some(
      (prescription) => !finalStates.includes(prescription.status),
    );

    const updateData: Prisma.PatientUpdateInput = {};

    if (!hasActivePrescription && patient.status !== 'pendingPrescription') {
      updateData.status = 'pendingPrescription';
    }

    if (patient.pharmacyId !== pharmacyId) {
      updateData.pharmacy = { connect: { id: pharmacyId } };

      const acceptIdentifyEvent: SegmentIdentify = {
        userId: patient.id,
        traits: {
          defaultPharmacy: pharmacyName,
        },
      };
      this.eventEmitter.emit(
        segmentIdentifyEvent.analyticIdentify,
        acceptIdentifyEvent,
      );
    }

    if (Object.keys(updateData).length > 0) {
      await this.prisma.patient.update({
        where: { id: patient.id },
        data: updateData,
      });
    }
  }

  private async identifyNewPatientAsActive(patient: Patient) {
    const newPatientStatuses: patientStatus[] = ['pendingPrescription'];

    if (patient.verificationStatus === 'verified') {
      newPatientStatuses.push('pendingApprovalFromDoctor');
    }

    if (!newPatientStatuses.includes(patient.status)) return;

    const identifyPatientEvent: SegmentIdentify = {
      userId: patient.userId,
      traits: {
        status: 'active',
      },
    };
    this.eventEmitter.emit(
      segmentIdentifyEvent.analyticIdentify,
      identifyPatientEvent,
    );
  }

  private async reActivatePatientIfCancelled(patient: Patient, doctor: Doctor) {
    if (patient.status === 'cancelled') {
      try {
        await this.patientService.unCancelPatient({
          patientId: patient.id,
          restoredBy: {
            type: 'DOCTOR',
            userId: doctor.userId,
            id: doctor.id,
          },
        });
      } catch (error) {
        console.error(`Error reactivating patient: ${error.message}`);
      }
    }
  }
}
