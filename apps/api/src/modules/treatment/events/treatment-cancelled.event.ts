import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { TreatmentUpdatedQueueEvent } from '@/modules/shared/events/treatment-topic.definition';
import { PrismaService } from '@modules/prisma/prisma.service';
import { segmentTrackEvents } from '@modules/shared/events';
import { SegmentTrack } from '@modules/shared/types/events';
import { StripeService } from '@modules/stripe/service/stripe.service';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class TreatmentCancelledEventListener {
  private logger = new Logger(TreatmentCancelledEventListener.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly stripeService: StripeService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-cancel-prescriptions-job',
    filter: ['cancelled'],
  })
  async handleTreatmentCancelledEvent({ payload }: TreatmentUpdatedQueueEvent) {
    this.logger.debug(`Handling treatment cancelled event`);

    const treatmentId = payload.treatment.id;

    const treatment = await this.prisma.treatment.findFirstOrThrow({
      where: { id: treatmentId },
      include: {
        initialProductPrice: {
          select: { product: { select: { name: true } } },
        },
        prescription: true,
      },
    });

    const invoicesToVoid = new Set<string>();
    const prescriptionsToDelete = new Set<string>();

    treatment.prescription.forEach((prescription) => {
      if (
        ['open', 'failed'].includes(prescription.status) &&
        prescription.stripeInvoiceId
      ) {
        invoicesToVoid.add(prescription.stripeInvoiceId);
      } else if (prescription.status === 'queued') {
        prescriptionsToDelete.add(prescription.id);
      }
    });

    for (const invoiceId of invoicesToVoid) {
      void this.stripeService.deleteDraft(invoiceId);
    }

    if (prescriptionsToDelete.size > 0) {
      await this.prisma.prescription.deleteMany({
        where: {
          id: { in: Array.from(prescriptionsToDelete) },
        },
      });
    }

    const treatmentCancelledEvent: SegmentTrack = {
      event: segmentTrackEvents.subscriptionCancelled.name,
      userId: treatment.patientId,
      properties: {
        productName: treatment.initialProductPrice.product.name,
      },
    };

    this.eventEmitter.emit(
      segmentTrackEvents.subscriptionCancelled.event,
      treatmentCancelledEvent,
    );
  }
}
