import { SendMessageUseCase } from '@/modules/chat/use-cases/send-message.use-case';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { segmentTrackEvents } from '@/modules/shared/events';
import { TreatmentUpdatedQueueEvent } from '@/modules/shared/events/treatment-topic.definition';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { SegmentTrack } from '@/modules/shared/types/events';
import { PrismaService } from '@modules/prisma/prisma.service';
import {
  getProductForAnalytics,
  replaceTemplate,
} from '@modules/shared/helpers/generic';
import {
  ActiveTreatmentProduct,
  RefillSystem,
  TreatmentMachineContext,
  TreatmentProduct,
} from '@modules/treatment/states/treatment.state';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  Pharmacy,
  Product,
  ProductForms,
  ProductPrice,
  Treatment,
} from '@prisma/client';

@Injectable()
export class TreatmentNotifyEventListener {
  private readonly logger: LoggerService;

  // Record of form types to message templates
  private readonly messageTemplates: Record<ProductForms, string> = {
    injectable:
      'Hi, your next refill of {refill} will be processing in 2 days. Please use {dose} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    oral: 'Hi, your next refill of {refill} will be processing in 2 days. Please use {dose} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    tablet:
      'Hi, your next refill of {refill} will be processing in 2 days. Please use {dose} {dosageTimeframe}. {dosageAdditionalMessage}',
  };

  // Record of form types to update message templates
  private readonly updateMessageTemplates: Record<ProductForms, string> = {
    injectable:
      'Update regarding your next refill: Please use {dose} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    oral: 'Update regarding your next refill: Please use {dose} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    tablet:
      'Update regarding your next refill: Please use {dose} {dosageTimeframe}. {dosageAdditionalMessage}',
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly eventEmitter: EventEmitter2,
    loggerFactory: LoggerFactory,
  ) {
    this.logger = loggerFactory.createLogger(TreatmentNotifyEventListener.name);
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-notify-next-refill',
    filter: ['treatment_notify_nextRefill'],
  })
  async nextRefill({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };

    // no other refills to notify about
    if (context.currentRefill >= context.refills) return;

    const activeProduct = context.activeProduct;
    const nextProduct = context.products[context.currentRefill + 1];
    let analyticsProduct: ReturnType<typeof getProductForAnalytics> | null =
      null;

    if (!nextProduct) return;

    //new treatments have dosageLabel in the context, not the same as dose wich is only the mg or ml, dosageLabel provides more info
    //by example: dose = 7.5 dosageLabel = 7.5ml - 50 units
    //for old treatments we need to fetch it from the DB
    if (nextProduct.dosageLabel) {
      analyticsProduct = getProductForAnalytics({
        dosageLabel: activeProduct.dosageLabel,
        form: activeProduct.form,
        label: activeProduct.name,
      });
    } else {
      const nextProductPrice = await this.prisma.productPrice.findFirst({
        where: {
          id: nextProduct.id,
        },
        include: { product: true },
      });
      analyticsProduct = getProductForAnalytics({
        dosageLabel: nextProductPrice?.dosageLabel,
        form: nextProductPrice?.product.form,
        label: nextProductPrice?.product.label,
      });
    }

    const trackDosageNotification: SegmentTrack = {
      event: segmentTrackEvents.newRefillSoon.name,
      userId: context.patientId,
      properties: {
        refillDate: context.nextRefillDate,
        ...analyticsProduct,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.newRefillSoon.event,
      trackDosageNotification,
    );
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-notify-next-dose',
    filter: ['treatment_notify_nextDose'],
  })
  async nextDose({ payload }: TreatmentUpdatedQueueEvent) {
    const treatment = await this.prisma.treatment.findFirst({
      where: { id: payload.treatment.id },
    });

    if (!treatment) {
      this.logger.error(`Treatment not found: ${payload.treatment.id}`, {
        treatmentId: payload.treatment.id,
        payload,
      });
      return;
    }

    if (!['inProgress.waitingBetweenRefills'].includes(treatment.status)) {
      this.logger.warn(`Treatment next dose message ignored`, {
        treatment: {
          id: payload.treatment.id,
          status: treatment.status,
        },
        payload,
      });
      return;
    }

    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    const activeProduct = context.activeProduct;

    let nextProduct: Partial<TreatmentProduct>;
    let price: ProductPrice & { product: Product };

    // Check if this treatment has been transferred to another treatment
    if (payload.treatment.transferredTo) {
      // Find the final treatment in the transfer chain
      const finalTreatment = await this.findFinalTransferredTreatment(
        payload.treatment.transferredTo,
      );

      if (!finalTreatment) {
        this.logger.error(
          `Could not find final transferred treatment starting from ${payload.treatment.transferredTo}`,
        );
        return;
      }

      // Use the first product from the final transferred treatment
      price = finalTreatment.initialProductPrice;

      // Make sure we can access the dose
      if (!price || !price.dosageDescription) return;

      nextProduct = { id: price.id, dose: price.dosageDescription };
    } else {
      // Use the next product from the current treatment
      nextProduct = context.products[context.currentRefill + 1];

      // don't send notification if next product is not found
      if (!nextProduct) return;

      price = await this.prisma.productPrice.findFirst({
        where: { id: nextProduct.id },
        include: { product: true },
      });

      if (!price) return;
    }
    const { conversationId, doctorUserId } =
      await this.getConversationAndPatientName(context.patientId);

    const message: string = this.generateRefillMessage(
      price,
      activeProduct,
      context.refillSystem,
    );

    await this.sendMessageUseCase.execute({
      content: message,
      contentType: 'text',
      role: 'Doctor',
      conversationId,
      userId: doctorUserId,
      type: 'system',
    });

    // send segment event
    const trackDosageNotification: SegmentTrack = {
      event: segmentTrackEvents.dosageNotification.name,
      userId: context.patientId,
      properties: {
        productName: activeProduct.name,
        currentDose: activeProduct.dose,
        newDose: nextProduct.dose,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.dosageNotification.event,
      trackDosageNotification,
    );

    // Check if this is a transferred treatment and it's injectable
    if (
      payload.treatment.transferredTo &&
      price.product.form === 'injectable'
    ) {
      // Send the transfer notification message directly
      // This handles the case where transfer happened before dose notification
      await this.sendTransferNotification(payload.treatment, context);
    }
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-notify-transfer-refill',
    filter: ['treatment_notify_transferRefill'],
  })
  async transferRefill({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };

    // This should only be called for treatments that were transferred
    if (!payload.treatment.transferredTo) {
      this.logger.error('Transfer refill event called without transferredTo', {
        payload,
      });
      return;
    }

    // Find the final treatment in the transfer chain
    const finalTreatment = await this.findFinalTransferredTreatment(
      payload.treatment.transferredTo,
    );

    if (!finalTreatment) {
      this.logger.error(
        `Could not find final transferred treatment starting from ${payload.treatment.transferredTo}`,
        { payload },
      );
      return;
    }

    // Get the product to check if it's injectable
    const price = finalTreatment.initialProductPrice;

    if (!price) {
      this.logger.error(
        'Product price not found for transfer refill notification',
        { payload },
      );
      return;
    }

    // Always handle the dose update for the transferred treatment
    await this.handleTransferDoseUpdate(payload.treatment, context);

    // Additionally, for injectable products, send the transfer notification
    if (price.product.form === 'injectable') {
      await this.sendTransferNotification(payload.treatment, context);
    }
  }

  private async handleTransferDoseUpdate(
    treatment: Treatment,
    context: TreatmentMachineContext,
  ) {
    const activeProduct = context.activeProduct;

    // Find the final treatment in the transfer chain
    const finalTreatment = await this.findFinalTransferredTreatment(
      treatment.transferredTo,
    );

    if (!finalTreatment) {
      this.logger.error(
        `Could not find final transferred treatment starting from ${treatment.transferredTo}`,
        { treatment },
      );
      return;
    }

    // Use the first product from the final transferred treatment
    const price = finalTreatment.initialProductPrice;

    // Make sure we can access the dose
    if (!price || !price.dosageDescription) {
      this.logger.error(
        'No dosage description found for transferred treatment',
        { treatment },
      );
      return;
    }

    const nextProduct = { id: price.id, dose: price.dosageDescription };

    // Get conversation info
    const { conversationId, doctorUserId } =
      await this.getConversationAndPatientName(context.patientId);

    // Calculate the new message (same format as in nextDose method)
    const newMessage = this.generateRefillMessage(
      price,
      activeProduct,
      context.refillSystem,
    );

    // Get the last system message from the conversation
    const lastSystemMessage = await this.prisma.conversationMessage.findFirst({
      where: { conversationId, type: 'system' },
      orderBy: { createdAt: 'desc' },
    });

    // If the last system message is the same as the new message, no need to send an update
    if (lastSystemMessage && lastSystemMessage.content === newMessage) {
      this.logger.debug(
        'New dose is the same as previous dose, no update needed',
        { treatment },
      );
      return;
    }

    // Generate update message using template system
    const updateMessage = this.generateUpdateMessage(
      price,
      context.refillSystem,
    );

    await this.sendMessageUseCase.execute({
      content: updateMessage,
      contentType: 'text',
      role: 'Doctor',
      conversationId,
      userId: doctorUserId,
      type: 'system',
    });

    // Send segment event
    const trackDosageNotification: SegmentTrack = {
      event: segmentTrackEvents.dosageNotification.name,
      userId: context.patientId,
      properties: {
        productName: activeProduct.name,
        currentDose: activeProduct.dose,
        newDose: nextProduct.dose,
        isUpdate: true,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.dosageNotification.event,
      trackDosageNotification,
    );
  }

  private async getConversationAndPatientName(patientId: string) {
    const result = await this.prisma.patient.findFirst({
      where: { id: patientId },
      select: {
        conversations: {
          where: { type: 'patientDoctor' },
          select: { id: true, type: true },
        },
        doctor: { select: { userId: true } },
      },
    });

    const patientDoctorConversation = result.conversations?.find(
      (c) => c.type === 'patientDoctor',
    );
    return {
      conversationId: patientDoctorConversation.id,
      doctorUserId: result.doctor.userId,
    };
  }

  private generateRefillMessage(
    price: ProductPrice & { product: Product },
    activeTreatmentProduct: ActiveTreatmentProduct,
    refillSystem: RefillSystem,
  ) {
    const form = price.product.form;

    // Get the template for this form type
    const messageTemplate =
      this.messageTemplates[form] || this.messageTemplates.injectable;

    const dose = `${price.dosageDescription} / ${price.milligrams}mg`;

    const templateData = {
      refill: activeTreatmentProduct.name,
      dose,
      dosageTimeframe: price.dosageTimeframe,
      dosageAdditionalMessage:
        refillSystem === 'scaling' && price.dosageAdditionalMessage
          ? price.dosageAdditionalMessage
          : '',
    };

    return replaceTemplate(messageTemplate, templateData);
  }

  private generateUpdateMessage(
    price: ProductPrice & { product: Product },
    refillSystem: RefillSystem,
  ) {
    // Get the form from metadata, defaulting to 'injectable' if not found
    const form = price.product.form;

    // Get the update template for this form type
    const updateTemplate =
      this.updateMessageTemplates[form] ||
      this.updateMessageTemplates.injectable;

    const dose = `${price.dosageDescription} / ${price.milligrams}mg`;

    const templateData = {
      dose,
      dosageTimeframe: price.dosageTimeframe,
      dosageAdditionalMessage:
        refillSystem === 'scaling' && price.dosageAdditionalMessage
          ? price.dosageAdditionalMessage
          : '',
    };

    return replaceTemplate(updateTemplate, templateData).trim();
  }

  /**
   * Recursively follows the transferredTo chain to find the final treatment.
   * This handles cases where a treatment has been transferred multiple times.
   * @param treatmentId - The ID of the treatment to start from
   * @param maxDepth - Maximum depth to follow to prevent infinite loops
   * @returns The final treatment in the transfer chain
   */
  private async findFinalTransferredTreatment(
    treatmentId: string,
    maxDepth: number = 10,
  ): Promise<
    | (Treatment & {
        initialProductPrice: ProductPrice & {
          product: Product & { pharmacy: Pharmacy };
        };
      })
    | null
  > {
    let currentTreatment = await this.prisma.treatment.findUnique({
      where: { id: treatmentId },
      include: {
        initialProductPrice: {
          include: { product: { include: { pharmacy: true } } },
        },
      },
    });

    if (!currentTreatment) {
      return null;
    }

    let depth = 0;
    const visitedIds = new Set<string>([currentTreatment.id]);

    // Follow the transfer chain
    while (currentTreatment.transferredTo && depth < maxDepth) {
      // Check for circular references
      if (visitedIds.has(currentTreatment.transferredTo)) {
        this.logger.error(
          `Circular transfer reference detected: ${currentTreatment.id} -> ${currentTreatment.transferredTo}`,
          { currentTreatment, treatmentId, depth, maxDepth, visitedIds },
        );
        break;
      }

      const nextTreatment = await this.prisma.treatment.findUnique({
        where: { id: currentTreatment.transferredTo },
        include: {
          initialProductPrice: {
            include: { product: { include: { pharmacy: true } } },
          },
        },
      });

      if (!nextTreatment) {
        this.logger.error(
          `Transferred treatment ${currentTreatment.transferredTo} not found`,
          { currentTreatment, treatmentId, depth, maxDepth, visitedIds },
        );
        break;
      }

      visitedIds.add(nextTreatment.id);
      currentTreatment = nextTreatment;
      depth++;
    }

    if (depth >= maxDepth) {
      this.logger.error(
        `Maximum transfer depth (${maxDepth}) reached for treatment ${treatmentId}`,
        { currentTreatment, treatmentId, depth, maxDepth, visitedIds },
      );
    }

    return currentTreatment;
  }

  /**
   * Finds the last treatment in the transfer chain that was actually active (delivered medication).
   * This is used to show transfers from the last pharmacy that delivered medication,
   * not from intermediate scheduled treatments that were cancelled.
   * @param treatmentId - The ID of the treatment to start from
   * @returns The last active treatment in the chain, or null if none found
   */
  private async findLastActiveTreatmentInChain(treatmentId: string): Promise<
    | (Treatment & {
        initialProductPrice: ProductPrice & {
          product: Product & { pharmacy: Pharmacy };
        };
      })
    | null
  > {
    let currentTreatment = await this.prisma.treatment.findUnique({
      where: { id: treatmentId },
      include: {
        initialProductPrice: {
          include: { product: { include: { pharmacy: true } } },
        },
      },
    });

    if (!currentTreatment) {
      return null;
    }

    // If this treatment was active (actually delivered medication), return it
    if (
      currentTreatment.status.includes('inProgress') ||
      currentTreatment.status === 'completed'
    ) {
      return currentTreatment;
    }

    // Otherwise, trace back through the transfer chain to find the last active treatment
    const visited = new Set<string>([currentTreatment.id]);

    while (currentTreatment) {
      // Find treatments that transferred TO this treatment
      const previousTreatment = await this.prisma.treatment.findFirst({
        where: {
          transferredTo: currentTreatment.id,
          id: { notIn: Array.from(visited) }, // Prevent circular references
        },
        include: {
          initialProductPrice: {
            include: { product: { include: { pharmacy: true } } },
          },
        },
      });

      if (!previousTreatment) {
        // No more treatments in the chain
        break;
      }

      visited.add(previousTreatment.id);

      // Check if this previous treatment was active
      if (
        previousTreatment.status.includes('inProgress') ||
        previousTreatment.status === 'completed'
      ) {
        return previousTreatment;
      }

      currentTreatment = previousTreatment;
    }

    // No active treatment found in the chain
    return null;
  }

  private async sendTransferNotification(
    treatment: Treatment,
    context: TreatmentMachineContext,
  ) {
    if (!treatment.transferredTo) {
      return;
    }

    // Find the final treatment in the transfer chain
    const finalTreatment = await this.findFinalTransferredTreatment(
      treatment.transferredTo,
    );

    if (!finalTreatment) {
      this.logger.error(
        `Could not find final transferred treatment starting from ${treatment.transferredTo}`,
        { treatment },
      );
      return;
    }

    // Find the last treatment that actually delivered medication (was active)
    const lastActiveTreatment = await this.findLastActiveTreatmentInChain(
      treatment.id,
    );

    if (!lastActiveTreatment) {
      this.logger.error(
        `No active treatment found in transfer chain for treatment ${treatment.id}`,
        { treatment, finalTreatment },
      );
      return;
    }

    const oldPharmacyName =
      lastActiveTreatment.initialProductPrice.product.pharmacy.name;
    const newPharmacyName =
      finalTreatment.initialProductPrice.product.pharmacy.name;
    const productName = finalTreatment.initialProductPrice.product.label;

    // Get additive benefit from the new pharmacy's product
    const additiveBenefit = (finalTreatment.initialProductPrice as any)
      .additiveBenefit as string | null;

    // Build dose mapping for all doses of the product
    const doseMapping = await this.buildDoseMapping(
      lastActiveTreatment.initialProductPrice.product.id,
      finalTreatment.initialProductPrice.product.id,
      oldPharmacyName,
      newPharmacyName,
    );

    // Generate the transfer message
    const message = this.generateTransferMessage(
      newPharmacyName,
      productName,
      additiveBenefit,
      doseMapping,
    );

    // Get conversation info
    const { conversationId, doctorUserId } =
      await this.getConversationAndPatientName(context.patientId);

    // Send the message
    await this.sendMessageUseCase.execute({
      content: message,
      contentType: 'text',
      role: 'Doctor',
      conversationId,
      userId: doctorUserId,
      type: 'system',
    });
  }

  private async buildDoseMapping(
    oldProductId: string,
    newProductId: string,
    oldPharmacyName: string,
    newPharmacyName: string,
  ): Promise<Array<{ oldDose: string; newDose: string }>> {
    // Get all product prices for both products
    const [oldProductPrices, newProductPrices] = await Promise.all([
      this.prisma.productPrice.findMany({
        where: {
          productId: oldProductId,
          active: true,
        },
        orderBy: {
          phase: 'asc',
        },
      }),
      this.prisma.productPrice.findMany({
        where: {
          productId: newProductId,
          active: true,
        },
        orderBy: {
          phase: 'asc',
        },
      }),
    ]);

    // Build mapping based on equivalence groups
    const mapping: Array<{ oldDose: string; newDose: string }> = [];

    for (const oldPrice of oldProductPrices) {
      // Find the corresponding new price by equivalence group
      const newPrice = newProductPrices.find(
        (np) =>
          np.equivalenceGroupId &&
          np.equivalenceGroupId === oldPrice.equivalenceGroupId,
      );

      if (newPrice && oldPrice.milligrams && newPrice.milligrams) {
        mapping.push({
          oldDose: `${oldPharmacyName} ${oldPrice.milligrams}mg dose`,
          newDose: `${newPharmacyName} ${newPrice.milligrams}mg dose`,
        });
      }
    }

    return mapping;
  }

  private generateTransferMessage(
    newPharmacyName: string,
    productName: string,
    additiveBenefit: string | null,
    doseMapping: Array<{ oldDose: string; newDose: string }>,
  ): string {
    let message = `In order to assure continued supply of the highest quality compounded GLP-1 medications, Willow maintains relationships with multiple pharmacies. It is sometimes necessary to change pharmacies both to improve quality and customer service as well as to ensure no disruption in supply.

Your ${productName} prescription will now be filled by ${newPharmacyName} and personalized to your needs.`;

    if (additiveBenefit) message += ` ${additiveBenefit}.`;

    if (doseMapping.length > 0) {
      message +=
        ' With this new formula, the dosing schedule may be slightly different:\n\n';
      message += doseMapping
        .map((dm) => `${dm.oldDose} → ${dm.newDose}`)
        .join('\n');
    }

    return message;
  }
}
