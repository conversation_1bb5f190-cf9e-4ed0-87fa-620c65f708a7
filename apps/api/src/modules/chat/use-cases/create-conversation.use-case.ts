import { PrismaService } from '@/modules/prisma/prisma.service';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { PatientUpdatedQueueEvent } from '@/modules/shared/events/patient-topic.definition';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { Injectable } from '@nestjs/common';
import { ConversationType } from '@prisma/client';

type ensureConversationTypeParams =
  | {
      conversationType: 'patientDoctor';
      doctorUserId: string;
      patientUserId: string;
    }
  | {
      conversationType: 'doctorAdmin';
      doctorUserId: string;
      patientUserId: string;
      adminUserId: string;
    };

@Injectable()
export class CreateConversationUseCase {
  private readonly logger: LoggerService;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(
      CreateConversationUseCase.name,
    );
  }

  @SnsConsume({
    topic: 'patient-updated',
    consumerGroup: 'create-chat-service',
    filter: ['doctor_assigned'],
  })
  async handleSnsMessage({ payload }: PatientUpdatedQueueEvent) {
    const { patient } = payload;
    return this.execute({
      doctorUserId: patient.doctor.userId,
      patientUserId: patient.id,
    });
  }

  async execute({
    doctorUserId,
    patientUserId,
  }: {
    doctorUserId: string;
    patientUserId: string;
  }) {
    return this.ensureConversationType({
      conversationType: 'patientDoctor',
      doctorUserId,
      patientUserId,
    }).catch((err) => {
      // Log and return null if one fails, but continue with others
      this.logger.error(
        err,
        {
          doctorUserId,
          patientUserId,
        },
        `Error ensuring conversation of type patientDoctor:`,
      );
      return null;
    });
  }

  async ensureConversationType(params: ensureConversationTypeParams) {
    const { conversationType, doctorUserId, patientUserId } = params;
    const adminUserId =
      conversationType === 'doctorAdmin' ? params.adminUserId : undefined;

    const conversation = await this.prismaService.conversation.upsert({
      where: {
        patientId_type: {
          patientId: patientUserId,
          type: conversationType,
        },
      },
      create: {
        userId: patientUserId,
        patientId: patientUserId,
        type: conversationType,
        status: conversationType === 'doctorAdmin' ? 'open' : 'active',
        assignedAdminId:
          conversationType === 'doctorAdmin' ? adminUserId : null,
      },
      update: {},
      include: {
        watcher: true,
      },
    });

    const watchersToCreate = [
      {
        userId: doctorUserId,
        conversationId: conversation.id,
      },
    ];

    if (conversationType === 'patientDoctor') {
      watchersToCreate.push({
        userId: patientUserId,
        conversationId: conversation.id,
      });
    } else if (
      /*NOTE: if the convo is a doctorAdmin and the adminUserId is not provided
      (like when the convo is created by a doctor) no watcher for admin will be created
      the watcher for admins can still be created when the admin sends a message in the convo
      or when the admin is assigned to the convo
      */
      conversationType == 'doctorAdmin' &&
      typeof adminUserId === 'string'
    ) {
      watchersToCreate.push({
        userId: adminUserId,
        conversationId: conversation.id,
      });
    }

    if (conversationType === 'patientDoctor') {
      // clear old watchers - old doctors
      await this.prismaService.conversationWatcher.deleteMany({
        where: {
          conversationId: conversation.id,
          userId: { not: patientUserId },
        },
      });
    }

    if (watchersToCreate.length > 0) {
      await this.prismaService.conversationWatcher.createMany({
        data: watchersToCreate,
        skipDuplicates: true,
      });
    }

    return conversation.id;
  }
}
