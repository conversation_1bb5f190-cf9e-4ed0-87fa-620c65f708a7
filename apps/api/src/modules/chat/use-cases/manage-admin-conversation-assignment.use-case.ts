import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ManageAdminConversationAssignmentUseCase {
  constructor(private readonly prismaService: PrismaService) {}

  async assignConversation({
    conversationId,
    adminUserId,
  }: {
    conversationId: string;
    adminUserId: string;
  }) {
    // Verify the conversation exists and is of type doctorAdmin
    const conversation = await this.prismaService.conversation.findFirst({
      where: {
        id: conversationId,
        type: 'doctorAdmin',
      },
    });

    if (!conversation) {
      throw new Error('Admin-doctor conversation not found');
    }

    // Verify the admin user exists and is an admin
    const admin = await this.prismaService.user.findFirst({
      where: {
        id: adminUserId,
        type: 'admin',
      },
      include: {
        admin: true,
      },
    });

    if (!admin || !admin.admin) {
      throw new Error('Admin user not found');
    }

    // Update conversation assignment
    const updatedConversation = await this.prismaService.conversation.update({
      where: { id: conversationId },
      data: {
        assignedAdminId: adminUserId,
        updatedAt: new Date(),
      },
    });

    // Ensure the admin is a watcher of the conversation
    await this.prismaService.conversationWatcher.upsert({
      where: {
        conversationId_userId: {
          conversationId,
          userId: adminUserId,
        },
      },
      create: {
        conversationId,
        userId: adminUserId,
      },
      update: {},
    });

    return updatedConversation;
  }

  async unassignConversation({ conversationId }: { conversationId: string }) {
    // Verify the conversation exists and is of type doctorAdmin
    const conversation = await this.prismaService.conversation.findFirst({
      where: {
        id: conversationId,
        type: 'doctorAdmin',
      },
    });

    if (!conversation) {
      throw new Error('Admin-doctor conversation not found');
    }

    // Remove assignment
    const updatedConversation = await this.prismaService.conversation.update({
      where: { id: conversationId },
      data: {
        assignedAdminId: null,
        updatedAt: new Date(),
      },
    });

    return updatedConversation;
  }

  async closeConversation({
    conversationId,
    adminUserId,
  }: {
    conversationId: string;
    adminUserId: string;
  }) {
    // Verify the conversation exists and is of type doctorAdmin
    const conversation = await this.prismaService.conversation.findFirst({
      where: {
        id: conversationId,
        type: 'doctorAdmin',
      },
    });

    if (!conversation) {
      throw new Error('Admin-doctor conversation not found');
    }

    // Close conversation and auto-unassign
    const updatedConversation = await this.prismaService.conversation.update({
      where: { id: conversationId },
      data: {
        status: 'closed', // Use 'closed' status for admin-doctor conversations
        assignedAdminId: null, // Auto-unassign when closing
        closedAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return updatedConversation;
  }

  async reopenConversation({ conversationId }: { conversationId: string }) {
    // Verify the conversation exists and is of type doctorAdmin
    const conversation = await this.prismaService.conversation.findFirst({
      where: {
        id: conversationId,
        type: 'doctorAdmin',
      },
    });

    if (!conversation) {
      throw new Error('Admin-doctor conversation not found');
    }

    // Reopen conversation
    const updatedConversation = await this.prismaService.conversation.update({
      where: { id: conversationId, type: 'doctorAdmin' },
      data: {
        status: 'open', // Use 'open' status for admin-doctor conversations
        closedAt: null,
        lastMessageFrom: null,
        lastMessageText: null,
        updatedAt: new Date(),
      },
    });

    return updatedConversation;
  }
}
