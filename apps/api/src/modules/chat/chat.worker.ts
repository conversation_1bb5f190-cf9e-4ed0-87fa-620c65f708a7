import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { LoggerFactory, LoggerService } from '../shared/logger/logger.service';
import { OrchestrationService } from '../shared/orchestration/orchestration.service';
import { CreateConversationUseCase } from './use-cases/create-conversation.use-case';

@Injectable()
export class ChatWorker {
  private readonly logger: LoggerService;
  private readonly disabled: boolean;

  constructor(
    private readonly prisma: PrismaService,
    private readonly orchestrationService: OrchestrationService,
    private readonly createConversationUseCase: CreateConversationUseCase,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.disabled =
      process.env.IS_CLI === 'true' ||
      (process.env.ENVIRONMENT !== 'local' &&
        !!process.env.NEXT_PUBLIC_ENVIRONMENT);

    this.logger = this.loggerFactory.createLogger(ChatWorker.name);
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async fixMissingConversationsCron() {
    if (this.disabled) return;

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'fixMissingConversationsCron-cron',
          ttl: 1000 * 60 * 4, // 5 minutes
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.fixMissingConversations();
        },
      );
    } catch (error) {
      this.logger.error('Error in fixMissingConversationsCron', error);
    }
  }
  // pendingApprovalFromDoctor
  async fixMissingConversations() {
    const result = await this.prisma.$queryRaw<
      {
        patient_user_id: string;
        doctor_user_id: string;
      }[]
    >`
      SELECT p."userId" AS patient_user_id, d."userId" AS doctor_user_id FROM "Patient" p
      INNER JOIN "Doctor" d ON p."doctorId" = d.id
      LEFT JOIN "Conversation" c ON c."patientId" = p.id AND c.type = 'patientDoctor'
      LEFT JOIN "ConversationWatcher" patient_watcher ON patient_watcher."conversationId" = c.id AND patient_watcher."userId" = p."userId"
      LEFT JOIN "ConversationWatcher" doctor_watcher ON doctor_watcher."conversationId" = c.id AND doctor_watcher."userId" = d."userId"
      WHERE
          p."doctorId" IS NOT NULL
          AND p.status != 'deleted'
          AND (patient_watcher.id IS NULL OR doctor_watcher.id IS NULL)
    `;

    this.logger.debug(
      `Found ${result.length} patients with missing patientDoctor conversation`,
    );

    for (const patient of result) {
      this.logger.log(
        `Fixing patient ${patient.patient_user_id} conversation with doctor`,
      );

      await this.createConversationUseCase.ensureConversationType({
        conversationType: 'patientDoctor',
        doctorUserId: patient.doctor_user_id,
        patientUserId: patient.patient_user_id,
      });
    }
  }
}
