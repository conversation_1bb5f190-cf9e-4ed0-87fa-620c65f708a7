import { CacheService } from '@/modules/cache/cache.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { Injectable } from '@nestjs/common';

import { PatientGroup, patientGroupList } from './get-patients-list.use-case';
import {
  activeTreatment,
  Pharmacies,
} from './utils/patient-group-filters.util';

@Injectable()
export class GetPatientsGroupCountsUseCase {
  private readonly logger: LoggerService;
  private readonly CACHE_KEY = 'admin:patients:group-counts';

  private readonly CACHE_FRESH = 3600; // 1 hour
  private readonly CACHE_STALE = 86400; // 1 day
  private readonly QUERY_TIMEOUT = 10000; // 10 seconds timeout per query
  private readonly COMPLEX_QUERY_TIMEOUT = 20000; // 20 seconds timeout for complex queries

  constructor(
    private readonly prismaService: PrismaService,
    private readonly cacheService: CacheService,
    loggerFactory: LoggerFactory,
  ) {
    this.logger = loggerFactory.createLogger(
      GetPatientsGroupCountsUseCase.name,
    );
  }

  async execute(): Promise<Record<PatientGroup, number>> {
    return this.executeWithCallback();
  }

  /**
   * Execute the use case with a streaming callback that gets called as each group count completes
   * @param callback Optional callback function that receives group and count as they complete
   */
  async executeStreaming(
    callback: (group: PatientGroup, count: number) => void,
  ): Promise<void> {
    await this.executeWithCallback(callback);
  }

  /**
   * Internal method that handles both streaming and non-streaming execution
   * @param callback Optional callback function for streaming results
   * @returns Record of all group counts when not streaming
   */
  private async executeWithCallback(
    callback?: (group: PatientGroup, count: number) => void,
  ): Promise<Record<PatientGroup, number>> {
    try {
      // Flag to track if we're dealing with cached data
      let isUsingCache = true;

      // Always use the flexible method to benefit from fresh/stale cache logic
      const result = await this.cacheService.flexible<
        Record<PatientGroup, number>
      >(this.CACHE_KEY, [this.CACHE_FRESH, this.CACHE_STALE], async () => {
        // This callback is called when:
        // 1. No cache exists
        // 2. Cache is expired (older than staleSeconds)
        // 3. Cache is stale and this is a background refresh

        // Mark that we're computing fresh data
        isUsingCache = false;

        // Initialize result object with all groups set to 0
        const result: Record<PatientGroup, number> = {} as Record<
          PatientGroup,
          number
        >;
        for (const group of patientGroupList) {
          result[group] = 0;
        }

        // Create an array to store promises for tracking completion
        const groupPromises = [];

        // Create a promise for each group that can be tracked individually
        for (const group of patientGroupList) {
          const promise = (async () => {
            try {
              // Get count for this group with timing
              const startTime = Date.now();
              const count = await this.getCountForGroup(group);
              const duration = Date.now() - startTime;

              // Update the result object
              result[group] = count;

              // Call the callback if provided (for streaming when computing fresh data)
              if (callback) {
                callback(group, count);
              }

              // Record timing data
              return { group, count, duration };
            } catch (error) {
              this.logger.error(
                `Error getting count for group ${group}:`,
                error,
              );
              // Update the result object with 0 for errors
              result[group] = 0;

              // Call the callback if provided (for streaming)
              if (callback) {
                callback(group, 0);
              }

              return { group, count: 0, duration: 0 };
            }
          })();

          groupPromises.push(promise);
        }

        // Wait for all promises to resolve
        await Promise.all(groupPromises);

        return result;
      });

      // If we have a callback and we used cached data, stream the cached results
      if (callback && isUsingCache && result) {
        for (const group of patientGroupList) {
          const count = result[group] || 0;
          callback(group, count);
        }
      }

      return result;
    } catch (error) {
      this.logger.error(error, {}, 'Error getting patient group counts');

      // Fallback: return empty counts if cache fails
      const fallbackResult: Record<PatientGroup, number> = {} as Record<
        PatientGroup,
        number
      >;
      for (const group of patientGroupList) {
        fallbackResult[group] = 0;
      }
      return fallbackResult;
    }
  }

  /**
   * Execute a promise with a timeout
   * @param promise The promise to execute
   * @param timeoutMs Timeout in milliseconds
   * @param errorMessage Error message if timeout occurs
   * @returns Promise result or throws error on timeout
   */
  private async executeWithTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    errorMessage: string,
  ): Promise<T> {
    let timeoutId: NodeJS.Timeout;

    // Create a promise that rejects after the specified timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      timeoutId = setTimeout(() => {
        reject(new Error(`Timeout: ${errorMessage}`));
      }, timeoutMs);
    });

    try {
      // Race the original promise against the timeout
      return await Promise.race([promise, timeoutPromise]);
    } finally {
      // Clear the timeout to prevent memory leaks
      clearTimeout(timeoutId!);
    }
  }

  /**
   * Get count for a specific patient group by dispatching to the appropriate query method
   */
  private async getCountForGroup(group: PatientGroup): Promise<number> {
    // Map groups to their dedicated count methods
    const countMethods: Record<PatientGroup, () => Promise<number>> = {
      all: this.countAllPatients.bind(this),
      active: this.countActivePatients.bind(this),
      inactive: this.countInactivePatients.bind(this),
      cancelled: this.countCancelledPatients.bind(this),
      banned: this.countBannedPatients.bind(this),
      deleted: this.countDeletedPatients.bind(this),
      'pending-patient-verification':
        this.countPendingPatientVerification.bind(this),
      'pending-doctor-verification':
        this.countPendingDoctorVerification.bind(this),
      rejected: this.countRejectedPatients.bind(this),
      onboarding: this.countOnboardingPatients.bind(this),
      'needs-reply': this.countNeedsReplyPatients.bind(this),
      'follow-up-needs-start': this.countFollowUpNeedsStart.bind(this),
      'follow-up-needs-complete': this.countFollowUpNeedsComplete.bind(this),
      'follow-up-needs-doctor-review':
        this.countFollowUpNeedsDoctorReview.bind(this),
      'no-follow-up': this.countNoFollowUp.bind(this),
      'failed-transaction': this.countFailedTransaction.bind(this),
      uncollectible: this.countUncollectible.bind(this),
      'card-expired': this.countCardExpired.bind(this),
      'red-rock': this.countRedRockPatients.bind(this),
      partell: this.countPartellPatients.bind(this),
      empower: this.countEmpowerPatients.bind(this),
      'prescriptions-definite-pause':
        this.countPrescriptionsDefinitePause.bind(this),
      'prescriptions-indefinite-paused':
        this.countPrescriptionsIndefinitePaused.bind(this),
      'prescriptions-complete': this.countPrescriptionsComplete.bind(this),
      'abandoned-checkout': this.countAbandonedCheckout.bind(this),
      'abandoned-verification': this.countAbandonedVerification.bind(this),
      'abandoned-treatment-selection':
        this.countAbandonedTreatmentSelection.bind(this),
      'abandoned-questionnaire': this.countAbandonedQuestionnaire.bind(this),
    };

    try {
      // Determine if this is a complex query that needs a longer timeout
      const isComplexQuery = [
        'abandoned-checkout',
        'abandoned-verification',
        'abandoned-treatment-selection',
        'prescriptions-complete',
        'card-expired',
        'follow-up-needs-start',
        'follow-up-needs-complete',
        'follow-up-needs-doctor-review',
        'no-follow-up',
      ].includes(group);

      const timeout = isComplexQuery
        ? this.COMPLEX_QUERY_TIMEOUT
        : this.QUERY_TIMEOUT;

      // Execute the appropriate query method with a timeout
      return await this.executeWithTimeout(
        countMethods[group](),
        timeout,
        `Query for group ${group} took too long`,
      );
    } catch (error) {
      this.logger.error(
        error,
        { group },
        `Error executing count query for group ${group}`,
      );
      return 0;
    }
  }

  /**
   * Count methods for each patient group
   * Each method contains a discrete SQL query for a specific patient group
   */
  private async countAllPatients(): Promise<number> {
    try {
      const result = await this.prismaService.readReplica().$queryRaw<
        { count: number }[]
      >`
        SELECT COUNT(*) as "count" FROM "User" WHERE "type" = 'patient'
      `;
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting all patients');
      return 0;
    }
  }

  private async countActivePatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status NOT IN ('deleted', 'banned', 'cancelled')
        AND EXISTS (
          SELECT 1 FROM "Treatment" t
          WHERE t."patientId" = p.id
          AND t."isCore" = TRUE
          AND t."status" = ANY('{${activeTreatment.join(',')}}')
          AND t."deletedAt" IS NULL
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting active patients:');
      return 0;
    }
  }

  private async countInactivePatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status != 'deleted'
        AND EXISTS (
          SELECT 1 FROM "Treatment" t
          WHERE t."patientId" = p.id
          AND (t."isCore" = FALSE OR (t."isCore" = FALSE AND t."status" != 'completed'))
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting inactive patients');
      return 0;
    }
  }

  private async countCancelledPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status = 'cancelled'
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting cancelled patients');
      return 0;
    }
  }

  private async countBannedPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status = 'banned'
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting banned patients');
      return 0;
    }
  }

  private async countDeletedPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status = 'deleted'
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting deleted patients');
      return 0;
    }
  }

  private async countPendingPatientVerification(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status = 'pendingUploadPhotos'
          OR (p.status = 'pendingApprovalFromDoctor' AND p."verificationStatus" = 'rejected')
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(
        error,
        {},
        'Error counting pending patient verification',
      );
      return 0;
    }
  }

  private async countPendingDoctorVerification(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE (p.status = 'pendingApprovalFromDoctor' OR p.status = 'onboardingCompleted')
          AND p."verificationStatus" IN ('pending', 'revalidated')
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(
        error,
        {},
        'Error counting pending doctor verification',
      );
      return 0;
    }
  }

  private async countRejectedPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status = 'pendingApprovalFromDoctor'
          AND p."verificationStatus" = 'rejected'
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting rejected patients');
      return 0;
    }
  }

  private async countOnboardingPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status = 'onboardingPending'
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting onboarding patients');
      return 0;
    }
  }

  private async countNeedsReplyPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status NOT IN ('deleted', 'banned', 'cancelled')
        AND EXISTS (
          SELECT 1 FROM "Conversation" c
          JOIN "ConversationWatcher" cw ON cw."conversationId" = c.id
          WHERE c."patientId" = p.id
          AND c."status" = 'active'
          AND cw."unreadMessages" > 0
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting needs-reply patients');
      return 0;
    }
  }

  private async countFollowUpNeedsStart(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status != 'deleted'
        AND EXISTS (
          SELECT 1 FROM "PatientFollowUp" f
          WHERE f."patientId" = p.id
          AND f."scheduledAt" <= CURRENT_DATE
          AND f."status" = 'scheduled'
          AND f."questionnaireState"->>'value' = 'medicalHistoryChanges'
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting follow-up needs start');
      return 0;
    }
  }

  private async countFollowUpNeedsComplete(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status != 'deleted'
        AND EXISTS (
          SELECT 1 FROM "PatientFollowUp" f
          WHERE f."patientId" = p.id
          AND f."status" = 'scheduled'
          AND f."questionnaireState"->>'value' != 'medicalHistoryChanges'
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting follow-up needs complete');
      return 0;
    }
  }

  private async countFollowUpNeedsDoctorReview(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status != 'deleted'
        AND EXISTS (
          SELECT 1 FROM "PatientFollowUp" f
          WHERE f."patientId" = p.id
          AND f."status" = 'completedByPatient'
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(
        error,
        {},
        'Error counting follow-up needs doctor review',
      );
      return 0;
    }
  }

  private async countNoFollowUp(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status != 'deleted'
        AND EXISTS (
          SELECT 1 FROM "Treatment" t
          WHERE t."patientId" = p.id
          AND t."isCore" = TRUE
          AND t."status" = ANY('{${activeTreatment.join(',')}}')
          AND NOT EXISTS (
            SELECT 1 FROM "PatientFollowUp" f
            WHERE f."treatmentId" = t.id
          )
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting no follow-up patients');
      return 0;
    }
  }

  private async countFailedTransaction(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE EXISTS (
          SELECT 1 FROM "Treatment" t
          WHERE t."patientId" = p.id
          AND t.status = 'failed'
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(
        error,
        {},
        'Error counting failed transaction patients',
      );
      return 0;
    }
  }

  private async countUncollectible(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE EXISTS (
          SELECT 1 FROM "Treatment" t
          WHERE t."patientId" = p.id
          AND t.status = 'uncollectible'
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting uncollectible patients');
      return 0;
    }
  }

  private async countCardExpired(): Promise<number> {
    try {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p.status != 'deleted'
          AND p.status != 'banned'
          AND p.status != 'cancelled'
          AND EXISTS (
            SELECT 1 FROM "PatientPaymentMethod" m
            WHERE m."patientId" = p.id
            AND m."default" = TRUE
            AND m.data ? 'card'
            AND (
              CAST((m.data->>'card')::json->>'exp_year' AS INTEGER) < ${currentYear}
              OR (CAST((m.data->>'card')::json->>'exp_year' AS INTEGER) = ${currentYear}
                  AND CAST((m.data->>'card')::json->>'exp_month' AS INTEGER) < ${currentMonth})
            )
          )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting card expired patients');
      return 0;
    }
  }

  private async countRedRockPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p."pharmacyId" IN (SELECT id FROM "Pharmacy" WHERE "name" = '${Pharmacies.RED_ROCK}')
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting Red Rock patients');
      return 0;
    }
  }

  private async countPartellPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p."pharmacyId" IN (SELECT id FROM "Pharmacy" WHERE "name" = '${Pharmacies.PARTELL}')
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting Partell patients');
      return 0;
    }
  }

  private async countEmpowerPatients(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p."pharmacyId" IN (SELECT id FROM "Pharmacy" WHERE "name" = '${Pharmacies.EMPOWER}')
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting Empower patients');
      return 0;
    }
  }

  private async countPrescriptionsDefinitePause(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE EXISTS (
          SELECT 1 FROM "Treatment" t
          WHERE t."patientId" = p.id
          AND t."state"->>'value' = 'paused'
          AND (t."state"->'context'->>'nextEventIn') IS NOT NULL
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(
        error,
        {},
        'Error counting prescriptions definite pause',
      );
      return 0;
    }
  }

  private async countPrescriptionsIndefinitePaused(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE EXISTS (
          SELECT 1 FROM "Treatment" t
          WHERE t."patientId" = p.id
          AND t."state"->>'value' = 'paused'
          AND (t."state"->'context'->>'nextEventIn') IS NULL
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(
        error,
        {},
        'Error counting prescriptions indefinite paused',
      );
      return 0;
    }
  }

  private async countPrescriptionsComplete(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE EXISTS (
          SELECT 1 FROM "Treatment" t
          WHERE t."patientId" = p.id
          AND t."isCore" = TRUE
          AND t."status" = 'completed'
        )
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting prescriptions complete');
      return 0;
    }
  }

  private async countAbandonedCheckout(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p."status" = 'onboardingPending'
          AND p."onboardingState"::jsonb ? 'context'
          AND p."onboardingState"::jsonb->'context' ? 'id-photo'
          AND p."onboardingState"::jsonb->'context' ? 'face-photo'
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting abandoned checkout');
      return 0;
    }
  }

  private async countAbandonedVerification(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p."status" = 'onboardingPending'
          AND p."onboardingState"::jsonb ? 'context'
          AND NOT (p."onboardingState"::jsonb->'context' ? 'id-photo')
          AND NOT (p."onboardingState"::jsonb->'context' ? 'face-photo')
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting abandoned verification');
      return 0;
    }
  }

  private async countAbandonedTreatmentSelection(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p."status" = 'onboardingPending'
          AND (p."onboardingState"::jsonb->>'value' = 'selectTreatmentType'
            OR p."onboardingState"::jsonb->>'value' = 'selectTreatment')
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(
        error,
        {},
        'Error counting abandoned treatment selection',
      );
      return 0;
    }
  }

  private async countAbandonedQuestionnaire(): Promise<number> {
    try {
      const query = `
        SELECT COUNT(DISTINCT p.id) as "count"
        FROM "Patient" p
        WHERE p."status" = 'onboardingPending'
          AND p."onboardingState"::jsonb ? 'value'
          AND p."onboardingState"::jsonb->'value' ? 'questionnaire'
      `;

      const result = await this.prismaService
        .readReplica()
        .$queryRawUnsafe<{ count: number }[]>(query);
      return result[0]?.count ? Number(result[0].count) : 0;
    } catch (error) {
      this.logger.error(error, {}, 'Error counting abandoned questionnaire');
      return 0;
    }
  }
}
