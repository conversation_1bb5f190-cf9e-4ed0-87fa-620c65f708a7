import { PrismaService } from '@/modules/prisma/prisma.service';
import { StripeService } from '@/modules/stripe/service/stripe.service';
import { TreatmentMachineContext } from '@modules/treatment/states/treatment.state';
import { Injectable, NotFoundException, StreamableFile } from '@nestjs/common';
import { String } from 'aws-sdk/clients/cloudsearchdomain';
import { formatInTimeZone } from 'date-fns-tz';
import * as PDFDocument from 'pdfkit';

import { addSvgWillowLogo } from '../patient.generics';

@Injectable()
export class PatientGetLetterOfMedicalNecessityUseCase {
  constructor(
    private readonly prisma: PrismaService,
    private readonly stripeService: StripeService,
  ) {}

  async execute(patientId: string) {
    const document = await this.generatePdf(patientId);
    return new StreamableFile(document, {
      type: 'application/pdf',
      disposition: 'attachment; filename="letter-of-medical-necessity.pdf"',
    });
  }

  async generatePdf(patientId: String) {
    const documentData = await this.getDocumentData(patientId);

    const doc: PDFDocument = new PDFDocument();

    doc.fontSize(11);
    this.addHeader(doc);
    this.addFooter(doc);
    doc.on('pageAdded', () => {
      this.addHeader(doc);
      this.addFooter(doc);
    });

    doc.moveTo(72, 150).lineTo(525, 150).stroke();
    doc.moveTo(72, 170).lineTo(525, 170).stroke();
    doc.moveTo(72, 190).lineTo(525, 190).stroke();

    doc.moveTo(306, 150).lineTo(306, 190).stroke();

    doc.moveTo(72, 150).lineTo(72, 190).stroke();
    doc.moveTo(525, 150).lineTo(525, 190).stroke();

    doc.text(`Dr. ${documentData.doctor.fullName}`, 78, 155);
    doc.text(`<EMAIL>`, doc.x, 175);

    doc.text(
      `Prescription No.: ${documentData?.stripeInvoice?.number || 'N/A'}`,
      312,
      155,
    );

    doc.text(
      `Date: ${formatInTimeZone(documentData.activePrescription.createdAt, 'UTC', 'MM/dd/yyyy')}`,
      doc.x,
      175,
    );

    doc.text(documentData.activeProductPrice.compoundName || 'N/A', 72, 210);
    doc.moveDown();
    doc.text(documentData.activeProductPrice.patientDirections || 'N/A');

    doc.moveDown(2);
    doc.text(`PATIENT:  ${documentData.patient.fullName}`);
    doc.moveDown();
    doc.text(`DOB: ${documentData.patient.birthDate}`);
    doc.moveDown();
    doc.text(`ADDRESS: ${documentData.patient.fullAddress}`);
    doc.moveDown();
    doc.text(`CONTACT: ${documentData.patient.phone}`);

    doc.addPage();
    //== SECOND PAGE ==

    this.boldText(doc, 'After Visit Summary');

    doc.moveTo(72, 170).lineTo(525, 170).stroke();
    doc.moveTo(72, 190).lineTo(525, 190).stroke();
    doc.moveTo(72, 210).lineTo(525, 210).stroke();
    doc.moveTo(72, 230).lineTo(525, 230).stroke();

    doc.moveTo(306, 170).lineTo(306, 230).stroke();

    doc.moveTo(72, 170).lineTo(72, 230).stroke();
    doc.moveTo(525, 170).lineTo(525, 230).stroke();

    doc.text(
      `Appointment Date: ${documentData.patient.acceptanceDate}`,
      78,
      175,
    );
    doc.text(`Patient: ${documentData.patient.fullName}`, 78, 195);
    doc.text(`DOB: ${documentData.patient.birthDate}`, 78, 215);

    doc.x = 310;
    doc.y = 175;
    doc.text(`Physician: ${documentData.doctor.fullName}`, 310, 175);
    doc.text('Clinic: Willow Health Services', 310, 195);
    doc.text('Contact: <EMAIL>', 310, 215);

    doc.x = 72;
    doc.y += 10;
    this.italicText(
      doc,
      'Thank you for choosing Willow Health Services for your recent medical appointment. This after-visit summary will provide you with a concise yet comprehensive overview of your visit, encompassing diagnoses, treatment, prescribed medications and key recommendations. We encourage you to view this summary attentively and reach out to us should you have any questions or concerns.',
    );

    doc.moveDown();
    this.boldText(doc, 'Diagnosis');
    doc.moveDown();
    doc.text(
      'During the visit, the doctor identified E66.9 obesity, unspecified as the primary medical concern. This diagnosis serves as the foundation for the treatment plan determined in the appointment.',
    );
    doc.moveDown();
    this.boldText(doc, 'Treatment Plan');
    doc.moveDown();
    doc.text(
      `Your treatment plan is a medication-assisted weight loss program, which involves the GLP-1 medication compounded with ${documentData.activeProductPrice.product.label}. This treatment was prescribed to address the underlying condition and alleviate associated symptoms effectively.`,
    );
    doc.moveDown();
    this.boldText(doc, 'Medications Prescribed');
    doc.moveDown();
    doc.text(
      `To support your treatment plan, the following medications have been prescribed: ${documentData.firstProductPrice.compoundName}.`,
    );
    doc.moveDown();
    this.boldText(doc, 'Follow-Up Instructions');
    doc.moveDown();
    doc.text(
      ` To monitor your progress and ensure optimal health outcomes, it is recommended to schedule a follow-up appointment with Dr. ${documentData.doctor.fullName} after 16 weeks. During this follow-up visit, further assessment and adjustments to your treatment plan, if necessary, will be discussed.,
    `,
    );
    doc.moveDown();
    this.boldText(doc, 'Conclusion');
    doc.moveDown();
    doc.text(
      'In conclusion, this After Visit Summary serves as a comprehensive guide to your recent medical consultation at Willow Health Services. Based on their interactions with you and your individual assessment during the intake process, your Willow doctor has determined that you would benefit from a personalized version of the medication; therefore, a compounded form of the medication is being prescribed. We remain committed to your well-being and encourage you to engage with us for any further assistance or clarification. Your health is our priority, and we look forward to continuing to support you in achieving optimal health outcomes.',
    );
    doc.end();
    return doc;
  }

  private boldText(doc: PDFDocument, text, ...args) {
    const docFontName = doc._font.name;
    doc
      .font(`${docFontName}-Bold`)
      .text(text, ...args)
      .font(docFontName);
  }

  private italicText(doc: PDFDocument, text, ...args) {
    const docFontName = doc._font.name;
    doc
      .font(`${docFontName}-Oblique`)
      .text(text, ...args)
      .font(docFontName);
  }

  private addHeader(doc: PDFDocument) {
    doc.x = 72;
    doc.y = 72;
    doc.rect(50, 50, 500, 80).fill('#2F4C78').stroke();
    doc.fillColor('white');
    doc.text('Willow Health Services');
    doc.text('startwillow.com');
    doc.text('<EMAIL>');
    doc.fillColor('black');
    addSvgWillowLogo(doc, 403, 72, '#FFFFFF');
    doc.restore();
    doc.x = 72;
    doc.y = 155;
  }

  /**
   * Adds a footer text to the bottom of the page.
   * NOTE: Call this method rigth after creating a new page because it reset the cursor position.
   */
  private addFooter(doc: PDFDocument) {
    doc.rect(50, 730, 500, 30).fill('#2F4C78').stroke();
    doc.fillColor('white');
    const bottomMargin = doc.page.margins.bottom;
    doc.page.margins.bottom = 0;
    doc.text('Willow Health Services', 72, 740, { align: 'left' });
    doc.text('startwillow.com', 72, 740, { align: 'center' });
    doc.text('<EMAIL>', 72, 740, { align: 'right' });
    doc.fillColor('black');
    doc.x = 72;
    doc.y = 155;
    doc.page.margins.bottom = bottomMargin;
  }

  async getDocumentData(patientId: string) {
    const patient = await this.prisma.patient.findFirst({
      where: { id: patientId },
      include: {
        user: true,
        shippingAddresses: {
          where: { default: true },
          include: { state: true },
        },
      },
    });

    if (!patient) throw new Error('Patient not found');

    const doctor = await this.prisma.doctor.findFirst({
      where: {
        id: patient.doctorId,
      },
      include: {
        user: true,
        prescribesIn: {
          where: {
            stateId: patient.stateId,
          },
        },
      },
    });

    if (!doctor) throw new Error('Patient must be accepted first');

    const lastTreatment = await this.prisma.treatment.findFirst({
      where: {
        patientId: patient.id,
        status: {
          in: [
            'completed',
            'inProgress.waitingForPrescription',
            'inProgress.waitingBetweenRefills',
            'paused',
          ],
        },
        isCore: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!lastTreatment) throw new Error('No treatment found');

    const treatmentStateContext: TreatmentMachineContext = (
      lastTreatment.state as any
    ).context;

    if (
      !treatmentStateContext.products ||
      treatmentStateContext.products.length === 0
    ) {
      throw new Error('No products found in treatment');
    }

    const firstProductPrice = await this.prisma.productPrice.findFirst({
      where: { id: treatmentStateContext.products[0].id },
      include: {
        product: true,
      },
    });
    const activeProductPrice =
      treatmentStateContext.activeProduct?.id ==
      treatmentStateContext.products[0].id
        ? firstProductPrice
        : await this.prisma.productPrice.findFirst({
            where: { id: treatmentStateContext.activeProduct?.id },
            include: {
              product: true,
            },
          });

    if (
      !firstProductPrice.compoundName ||
      !firstProductPrice.patientDirections ||
      !activeProductPrice.compoundName ||
      !activeProductPrice.patientDirections
    ) {
      throw new NotFoundException(
        'missing compound name or patient directions',
      );
    }

    const activePrescription = await this.prisma.prescription.findFirst({
      where: {
        treatmentId: lastTreatment.id,
        productPriceId: activeProductPrice.id,
      },
    });

    const stripeInvoice = await this.stripeService
      .client()
      .invoices.retrieve(activePrescription.stripeInvoiceId);

    const doctorFullName = `${doctor.user.firstName} ${doctor.user.lastName}`;
    const doctorLicense = doctor.prescribesIn[0].licenseNumber || 'N/A';
    const doctorNpi = doctor.npiNumber || 'N/A';

    const patientFullName = `${patient.user.firstName} ${patient.user.lastName}`;
    const patientGender = patient.gender == 'male' ? 'M' : 'F';
    const patientBirthDate = new Date(patient.birthDate).toLocaleDateString();
    const patientAcceptanceDate = new Date(
      patient.acceptedAt,
    ).toLocaleDateString();
    const patientPhone = patient.user.phone;
    const patientFullAddress = `${patient.shippingAddresses[0].address1} ${patient.shippingAddresses[0].city}, ${patient.shippingAddresses[0].state.code}, ${patient.shippingAddresses[0].zip}`;

    return {
      doctor: {
        fullName: doctorFullName,
        license: doctorLicense,
        npi: doctorNpi,
      },
      patient: {
        fullName: patientFullName,
        gender: patientGender,
        birthDate: patientBirthDate,
        acceptanceDate: patientAcceptanceDate,
        phone: patientPhone,
        fullAddress: patientFullAddress,
      },
      firstProductPrice,
      activeProductPrice,
      activePrescription,
      stripeInvoice,
    };
  }
}
