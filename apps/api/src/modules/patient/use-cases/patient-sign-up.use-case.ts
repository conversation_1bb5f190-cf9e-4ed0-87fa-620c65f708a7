import { IncomingHttpHeaders } from 'node:http';
import { sanitizeMachineStateForDB } from '@/adapters/persistence/database/patient.persistence';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { ContextService } from '@/modules/context/context.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { ReferralService } from '@/modules/referral/referral.service';
import { LoggerFactory } from '@/modules/shared/logger/logger.service';
import { CognitoService } from '@modules/auth/cognito.service';
import { OnboardingCookieService } from '@modules/onboarding/services/onboarding-cookie.service';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import {
  getDefaultOnboardingVersion,
  ONBOARDING_MACHINE_REGISTRY,
  OnboardingVersion,
} from '@modules/onboarding/states/versions';
import { PatientSignUpDto } from '@modules/patient/dto/patient-sign-up.dto';
import { timezones } from '@modules/shared/constants/state-timezone';
import { CognitoError } from '@modules/shared/errors/cognito.error';
import {
  segmentIdentifyEvent,
  segmentTrackEvents,
} from '@modules/shared/events';
import { formatPhoneNumber } from '@modules/shared/helpers/generic';
import { UtmService } from '@modules/shared/services/utm.service';
import { SegmentIdentify, SegmentTrack } from '@modules/shared/types/events';
import { PatientSignInOutput } from '@modules/shared/types/user/user.types';
import { StripeService } from '@modules/stripe/service/stripe.service';
import { Injectable, LoggerService } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Patient, State, User } from '@prisma/client';
import * as dayjs from 'dayjs';

@Injectable()
export class PatientSignUpUseCase {
  private readonly logger: LoggerService;

  constructor(
    private readonly cognitoService: CognitoService,
    private readonly prisma: PrismaService,
    private readonly onboardingService: OnboardingStateService,
    private readonly onboardingStateService: OnboardingStateService,
    private readonly stripeService: StripeService,
    private readonly eventEmitter: EventEmitter2,
    private readonly utmService: UtmService,
    private readonly auditService: AuditService,
    private readonly referralService: ReferralService,
    private readonly cookieService: OnboardingCookieService,
    private readonly contextService: ContextService,
    private readonly loggerFactory: LoggerFactory,
  ) {
    this.logger = this.loggerFactory.createLogger(PatientSignUpUseCase.name);
  }

  async execute(
    data: PatientSignUpDto,
    headers?: IncomingHttpHeaders,
  ): Promise<PatientSignInOutput> {
    const requestHeaders =
      headers || this.contextService.getRequestHeaders() || {};
    const {
      email,
      password,
      firstName,
      lastName,
      phone,
      state,
      getPromotionsSMS,
      promoCoupon: _promoCoupon,
      referralCode,
    } = data;
    const role = 'Patient';

    // can only use one of promoCoupon or referralCode
    const promoCoupon: string | undefined = referralCode
      ? undefined
      : _promoCoupon;

    let onboardingVersion: OnboardingVersion = getDefaultOnboardingVersion();

    // Check if cookie has a version (for consistency with pre-signup flow)
    const cookie = this.cookieService.getCookie();
    if (cookie?.version && ONBOARDING_MACHINE_REGISTRY[cookie.version]) {
      onboardingVersion = cookie.version;
    }

    // get the state id
    const { id: stateId } = await this.prisma.state.findFirstOrThrow({
      where: { code: state, enabled: true },
      select: { id: true },
    });

    // validate promo coupon if provided
    if (promoCoupon) {
      const { valid } = await this.stripeService.getCoupon(promoCoupon);
      if (!valid) {
        throw new Error('Invalid promo coupon code');
      }
    }

    let cognitoUserId: string;

    let newUser: User & {
      patient: Patient & {
        state: State;
      };
    } = null;
    try {
      newUser = await this.prisma.$transaction(
        async (prisma) => {
          // Get the current state from the cookie
          let onboardingState;
          const cookie = this.cookieService.getCookie();

          if (cookie?.stateSnapshot && cookie.version === onboardingVersion) {
            // We're being called from the pre-signup create account flow
            // The state machine should already be in the correct state (preSignup.creatingUser)
            // After successful user creation, the calling use case will transition it to questionnaire.age
            // For now, we'll save the current state as-is
            onboardingState = cookie.stateSnapshot;
          } else {
            // No cookie or version mismatch - use initial states
            onboardingState = await this.onboardingService.buildInitialStates(
              onboardingVersion as OnboardingVersion,
            );
          }

          // create user in cognito, type Patient
          const cognitoUser = await this.cognitoService.signUp(
            email,
            password,
            role,
          );
          // create a new user and related patient in the database
          cognitoUserId = cognitoUser['sub'];

          return prisma.user.create({
            data: {
              id: cognitoUserId,
              email,
              firstName,
              lastName,
              phone,
              type: 'patient',
              patient: {
                create: {
                  id: cognitoUserId,
                  getPromotionsSMS,
                  stateId,
                  onboardingState: sanitizeMachineStateForDB(onboardingState),
                  promoCoupon: promoCoupon,
                  referralCode: this.referralService.generateReferralCode(),
                  onboardingVersion: onboardingVersion,
                },
              },
            },
            include: {
              patient: {
                include: {
                  state: true,
                },
              },
            },
          });
        },
        {
          timeout: 30_000,
        },
      );
    } catch (e) {
      // Check if the error is because user already exists in database
      if (e instanceof CognitoError && e.message.includes('already exist')) {
        try {
          // Try to sign in with the provided credentials
          const tokens = await this.cognitoService.signIn(
            email,
            password,
            role,
          );

          // If sign-in successful, get existing user and return sign-in response
          const user = await this.prisma.user.findFirst({
            where: { email, type: 'patient', deletedAt: null },
            include: {
              patient: { include: { state: true } },
            },
          });

          if (!user || user.patient.status === 'banned') {
            throw new Error('email_already_exists');
          }

          const onboarding =
            await this.onboardingStateService.getCurrentOnboardingState(
              user.patient.onboardingVersion as OnboardingVersion,
              user.patient.onboardingState,
            );

          return {
            accessToken: tokens.getAccessToken().getJwtToken(),
            refreshToken: tokens.getRefreshToken().getToken(),
            role,
            patientId: user.patient.id,
            onboarding,
            status: user.patient.status,
            getStarted: {
              email: user.email,
              phone: user.phone,
              firstName: user.firstName,
              lastName: user.lastName,
              state: user.patient?.state?.code,
              gender: onboarding.context?.questionnaire?.gender,
              birthday: onboarding.context?.questionnaire?.birthDate,
            },
          };
        } catch (signInError) {
          // If sign-in fails, throw the original error which will show the enhanced dialog
          throw new Error('email_already_exists');
        }
      }

      this.logger.error(
        {
          err: e,
          email,
          cognitoUserId,
        },
        '[SIGN_UP] Error signing up the user, reversing changes',
      );

      if (cognitoUserId) {
        await this.cognitoService.deleteUser(cognitoUserId).catch((err) => {
          this.logger.error(
            err,
            {
              cognitoUserId,
            },
            '[SIGN_UP] Failed to delete user from Cognito during rollback',
          );
        });
      }

      throw e;
    }

    if (!newUser) {
      this.logger.warn(
        '[SIGN_UP] [INVESTIGATE] Failed to create user in the database',
        { email, cognitoUserId },
      );
      throw new Error('Failed to create user');
    }

    const user = newUser;

    if (referralCode) {
      await this.referralService.record({
        referralCode,
        newPatientId: user.id,
      });
    }

    void this.auditService.append({
      action: 'PATIENT_ACCOUNT_CREATED',
      actorType: 'PATIENT',
      actorId: user.patient.id,
      patientId: user.patient.id,
      resourceId: user.patient.id,
      resourceType: 'PATIENT',
      details: {
        date: new Date().toISOString(),
        state: user.patient.state.code,
      },
    });

    const signupDate = new Date(user.createdAt).getTime();
    await this.utmService.setupAndStoreCampaignDataToCache(
      requestHeaders,
      user.patient.id,
    );
    // Sent track event to segment
    const campaignData = await this.utmService.getCampaignDataFromCache(
      user.patient.id,
    );
    const trackEventAccountCreated: SegmentTrack = {
      event: segmentTrackEvents.accountCreated.name,
      userId: user.patient.id,
      properties: {
        signupDate,
        email: user.email,
        phone: formatPhoneNumber(user.phone),
        state: state,
        firstName: user.firstName,
        lastName: user.lastName,
        name: `${user.firstName} ${user.lastName}`,
        timezone: timezones[state]?.timezone,
        SMSOptIn: user.patient.getPromotionsSMS,
        gender: user.patient?.gender,
        birthday: user.patient?.birthDate
          ? dayjs(user.patient?.birthDate).add(1, 'month').format('MM/DD/YYYY')
          : '',
        firstCampaign: campaignData?.firstCampaign,
      },
      context: {
        ...campaignData,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.accountCreated.event,
      trackEventAccountCreated,
    );

    const trackEventSignUp: SegmentTrack = {
      event: segmentTrackEvents.signUp.name,
      userId: user.patient.id,
      properties: {},
      context: {
        ...campaignData,
      },
    };
    this.eventEmitter.emit(segmentTrackEvents.signUp.event, trackEventSignUp);

    const identifyEventAccountCreated: SegmentIdentify = {
      userId: user.patient.id,
      traits: {
        email: user.email,
        phone: formatPhoneNumber(user.phone),
        state: state,
        firstName: user.firstName,
        lastName: user.lastName,
        name: `${user.firstName} ${user.lastName}`,
        timezone: timezones[state]?.timezone,
        SMSOptIn: user.patient.getPromotionsSMS,
        signupDate,
        onboardingVersion: user.patient.onboardingVersion,
      },
    };
    this.eventEmitter.emit(
      segmentIdentifyEvent.analyticIdentify,
      identifyEventAccountCreated,
    );

    const tokens = await this.cognitoService.signIn(email, password, role);
    const onboarding =
      await this.onboardingStateService.getCurrentOnboardingState(
        user.patient.onboardingVersion as OnboardingVersion,
        user.patient.onboardingState,
      );

    return {
      accessToken: tokens.getAccessToken().getJwtToken(),
      refreshToken: tokens.getRefreshToken().getToken(),
      role: role,
      patientId: user.patient.id,
      onboarding,
      status: 'onboardingPending',
      getStarted: {
        email: user.email,
        phone: user.phone,
        firstName: user.firstName,
        lastName: user.lastName,
        state: user.patient?.state?.code,
        gender: onboarding.context?.questionnaire?.gender,
        birthday: onboarding.context?.questionnaire?.birthDate,
      },
    };
  }
}
