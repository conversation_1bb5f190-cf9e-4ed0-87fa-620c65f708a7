import { PatientPersistence } from '@/adapters/persistence/database/patient.persistence';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { DosespotPatient } from '@modules/dosespot/types/dosespot-patient';
import {
  segmentIdentifyEvent,
  segmentTrackEvents,
} from '@modules/shared/events';
import { OutboxerService } from '@modules/shared/outboxer/outboxer.service';
import { SegmentIdentify, SegmentTrack } from '@modules/shared/types/events';
import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class DoctorAcceptPatientUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly dosespotService: DosespotService,
    private readonly auditService: AuditService,
    private readonly outboxer: OutboxerService,
    private readonly patientPersistence: PatientPersistence,
  ) {}

  async execute(doctorUserId: string, patientId: string) {
    await this.prismaService.$transaction(async (prisma) => {
      const patient = await prisma.patient.findFirstOrThrow({
        where: { id: patientId, status: 'onboardingCompleted' },
        include: {
          user: true,
          pharmacy: true,
          shippingAddresses: {
            where: { default: true },
          },
          state: true,
        },
      });
      // check if patient has a doctor
      if (patient.doctorId) {
        throw new BadRequestException('Patient already has a doctor');
      }
      // Check if patient is already accepted
      if (patient.acceptedByUser) {
        throw new BadRequestException('Patient already accepted');
      }
      // validate doctor serving states with patient state
      const doctor = await this.prismaService.doctor.findFirstOrThrow({
        where: { userId: doctorUserId },
        include: {
          user: true,
          prescribesIn: { where: { stateId: patient.stateId } },
        },
      });

      // @todo check for max assignments per day, not relevant in MVP
      // Create this patient on dosespot
      const payload: DosespotPatient = {
        FirstName: patient.user.firstName,
        LastName: patient.user.lastName,
        DateOfBirth: patient.birthDate,
        Email: patient.user.email,
        Gender: patient.gender.toLocaleLowerCase() == 'male' ? 1 : 2,
        PrimaryPhoneType: 2,
        Active: true,
        PrimaryPhone: patient.user.phone.replace('+1', ''),
        Address1: patient.shippingAddresses[0].address1,
        Address2: patient.shippingAddresses[0].address2,
        City: patient.shippingAddresses[0].city,
        State: patient.state.code,
        ZipCode: patient.shippingAddresses[0].zip.padStart(5, '0'),
        Weight: patient.weight,
        Height: patient.height,
      };

      // create patient in DoseSpot and assign it to default pharmacy
      const dosespotPatient = await this.dosespotService.createPatient(
        payload,
        doctor.doseSpotClinicianId,
        patient.pharmacy.doseSpotPharmacyId,
      );

      // assign patient to doctor
      await prisma.doctorAssignment.create({
        data: {
          patientId: patientId,
          doctorId: doctor.id,
        },
      });
      // update patient with doctor id
      await prisma.patient.update({
        where: { id: patientId },
        data: {
          doctorId: doctor.id,
          doseSpotPatientId: String(dosespotPatient.Id),
          status: 'pendingApprovalFromDoctor',
          acceptedByUser: doctorUserId,
          acceptedAt: new Date(),
          updatedAt: new Date(),
        },
      });
      void this.auditService.append({
        patientId: patient.id,
        action: 'PATIENT_ACCEPTED',
        actorType: 'DOCTOR',
        actorId: doctor.id,
        resourceType: 'PATIENT',
        resourceId: patient.id,
        details: {
          doctorId: doctor.id,
          doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
          doseSpotPatientId: dosespotPatient.Id,
        },
      });

      await this.outboxer.enqueue(
        patient.id,
        'patient-updated',
        {
          event: 'doctor_assigned',
          patient: await this.patientPersistence.getPatientProfile(patientId, {
            prisma,
          }),
        },
        { prisma },
      );

      const event: SegmentTrack = {
        event: segmentTrackEvents.patientAccepted.name,
        userId: patient.id,
        properties: {
          doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
          doctorID: doctor.id,
          patientName: `${patient.user.firstName} ${patient.user.lastName}`,
          patientID: patient.id,
        },
      };
      this.eventEmitter.emit(segmentTrackEvents.patientAccepted.event, event);

      const acceptIdentifyEvent: SegmentIdentify = {
        userId: patient.id,
        traits: {
          doctorName: `${doctor.user.firstName} ${doctor.user.lastName}`,
          doctorID: doctor.id,
          defaultPharmacy: patient.pharmacy.name,
        },
      };
      this.eventEmitter.emit(
        segmentIdentifyEvent.analyticIdentify,
        acceptIdentifyEvent,
      );
    });
  }
}
