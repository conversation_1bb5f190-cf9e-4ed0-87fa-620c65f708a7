import {
  AnyOnboardingInput,
  getDefaultOnboardingVersion,
  ONBOARDING_MACHINE_REGISTRY,
  OnboardingVersion,
} from '@modules/onboarding/states/versions';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { Injectable } from '@nestjs/common';
import { createActor } from 'xstate';

export type OnboardingActor = Awaited<
  ReturnType<OnboardingStateService['getCurrentOnboardingActor']>
>;
export type OnboardingSnapshot = ReturnType<OnboardingActor['getSnapshot']>;

export type OnboardingProfile = Awaited<
  ReturnType<OnboardingStateService['getCurrentOnboardingState']>
>;

@Injectable()
export class OnboardingStateService {
  private readonly SECTION_NAMES = {
    preSignup: 'Account Creation',
    questionnaire: 'Health Questionnaire',
    selectTreatmentType: 'Select Your Medication',
    selectTreatment: 'Select Your Medication',
    identityVerification: 'Identity Verification',
    ssnCheck: 'Identity Verification',
    ssnSuccess: 'Identity Verification',
    info: 'Virtual Doctor Visit',
    uploadIdPhoto: 'Upload Your Photos',
    uploadFacePhoto: 'Upload Your Photos',
    shippingInfo: 'Checkout',
    paymentInfo: 'Checkout',
    reviewOrder: 'Checkout',
    confirmPayment: 'Checkout',
    processingPayment: 'Checkout',
    onboarded: 'Completed',
  };

  constructor() {}

  /**
   * Builds the initial states for the onboarding machine and the current questionnaire,
   * and it's persisted to the database when a new patient is created
   */
  async buildInitialStates(onboardingVersion?: OnboardingVersion) {
    const version = onboardingVersion || getDefaultOnboardingVersion();
    const onboarding = await this.buildVersionedOnboardingMachine(version);
    return onboarding.getSnapshot();
  }

  async getCurrentOnboardingActor(
    version: OnboardingVersion,
    snapshot: AnyOnboardingSnapshot,
    actors?: any,
  ): Promise<any> {
    switch (version) {
      case 'v1':
      case 'v2':
      case 'legacy-v1':
      case 'legacy-v2':
        return await this.buildVersionedOnboardingMachine(
          version,
          snapshot,
          actors,
        );
      default:
        throw new Error(`Unknown onboarding version: ${version}`);
    }
  }

  performTransition(actor: OnboardingActor, eventName: string, data?: any) {
    let snapshot = actor.getSnapshot();
    const event = data ? { type: eventName, value: data } : { type: eventName };
    if (!snapshot.can(event)) {
      throw new Error('Invalid transition');
    }
    actor.send(event);

    snapshot = actor.getSnapshot();
    if (snapshot.status === 'error') {
      throw new Error(
        `Error after transition: ${JSON.stringify(snapshot.value)}`,
      );
    }
    return actor;
  }

  private getStatePath(stateValue: any): string[] {
    if (typeof stateValue === 'string') {
      return [stateValue];
    }
    // Handle nested state objects like { questionnaire: 'age' }
    const keys = Object.keys(stateValue);
    if (keys.length === 1) {
      return [keys[0], ...this.getStatePath(stateValue[keys[0]])];
    }
    return [];
  }

  private getStateMetadata(snapshot: any): {
    stepName: string;
    percentage: number;
  } {
    const stateValue = snapshot.value;

    // Handle final state
    if (stateValue === 'onboarded') {
      return {
        stepName: this.SECTION_NAMES['onboarded'],
        percentage: 100,
      };
    }

    // Get state path (e.g., ['questionnaire', 'age'] or ['preSignup', 'stateSelection'])
    const statePath = this.getStatePath(stateValue);
    const rootState = statePath[0];

    // Navigate through the state configuration to find the current state's metadata
    let absoluteStep = 0;
    let name = '';
    let stateConfig = snapshot.machine.config.states;

    for (let i = 0; i < statePath.length; i++) {
      const stateName = statePath[i];
      if (stateConfig && stateConfig[stateName]) {
        // Get both step and name from the current state's meta if they exist
        if (stateConfig[stateName]?.meta) {
          absoluteStep = stateConfig[stateName].meta.step || absoluteStep;
          name = stateConfig[stateName].meta.name || name;
        }

        // Navigate to nested states for the next iteration
        if (i < statePath.length - 1 && stateConfig[stateName].states) {
          stateConfig = stateConfig[stateName].states;
        }
      }
    }

    // If no name was found in meta, fall back to SECTION_NAMES
    if (!name) {
      name = this.SECTION_NAMES[rootState] || '';
    }

    // Calculate timeline-based percentage
    let percentage = 0;

    if (rootState === 'preSignup') {
      // Timeline 1: Pre-signup (4 steps)
      const currentStep = Math.min(absoluteStep, 4);
      percentage = Math.round((currentStep / 4) * 100);
    } else {
      // Timeline 2: Main onboarding (questionnaire + post-questionnaire = 24 steps)
      let currentStep = 0;

      if (rootState === 'questionnaire') {
        // Questionnaire steps: absolute steps 5-19 map to timeline steps 1-15
        currentStep = absoluteStep - 4; // Step 5 becomes step 1, step 19 becomes step 15
      } else {
        // Post-questionnaire states
        const postQuestionnaireStepMap: Record<string, number> = {
          selectTreatmentType: 16,
          selectTreatment: 17,
          info: 18,
          uploadIDPhoto: 19,
          uploadFacePhoto: 20,
          visitCompletion: 21,
          summary: 22,
          shipping: 23,
          payment: 24,
        };

        currentStep = postQuestionnaireStepMap[rootState] || 16;
      }

      percentage = Math.round((currentStep / 24) * 100);
    }

    return {
      stepName: name,
      percentage,
    };
  }

  async getCurrentOnboardingState(
    version: OnboardingVersion,
    snapshot: any,
  ): Promise<any> {
    const actor = await this.getCurrentOnboardingActor(version, snapshot);

    const onboardingSnapshot = actor.getSnapshot();
    const { stepName, percentage } = this.getStateMetadata(onboardingSnapshot);

    // Get available events from the current state without executing guards
    const stateValue = onboardingSnapshot.value;
    const statePath = this.getStatePath(stateValue); // Use existing helper method

    // Navigate the machine configuration to find the current state's events
    let events: string[] = [];
    try {
      // Access the machine config through the actor
      const machineConfig = (actor as any).logic?.config;

      if (machineConfig?.states) {
        // Navigate to the current state node
        let currentStateConfig = machineConfig.states;

        for (const key of statePath) {
          if (currentStateConfig[key]) {
            // Get events from the current level
            const stateNode = currentStateConfig[key];
            if (stateNode?.on) {
              const stateEvents = Object.keys(stateNode.on).filter(
                (event) => event !== '',
              );
              events.push(...stateEvents);
            }

            // Navigate deeper if there are nested states
            currentStateConfig = currentStateConfig[key].states || {};
          }
        }
      }
    } catch (error) {
      console.warn('Could not extract events from machine config:', error);
      events = [];
    }

    events = [...new Set(events)];

    return {
      state: onboardingSnapshot.value,
      context: onboardingSnapshot.context,
      events,
      stepName,
      percentage,
    };
  }

  private async buildVersionedOnboardingMachine(
    version: OnboardingVersion,
    snapshot?: AnyOnboardingSnapshot,
    actors?: any,
  ) {
    const versionedMachine = ONBOARDING_MACHINE_REGISTRY[version];

    if (!versionedMachine) {
      throw new Error(`Onboarding version ${version} not found`);
    }

    // Provide actors if they are given
    const machine = actors
      ? versionedMachine.provide({ actors })
      : versionedMachine;

    const actor = snapshot
      ? createActor(machine, { snapshot })
      : createActor(machine, {
          input: {
            version,
            existingData: {},
          } as AnyOnboardingInput,
        });

    return actor.start();
  }
}
