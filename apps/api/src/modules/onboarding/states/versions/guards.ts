import * as dayjs from 'dayjs';

import type { QuestionnaireStepData } from './types/onboarding-v1.types';

// Generic guard type that works with any context and event types
type GuardFunction<TContext = any, TEvent = any> = (
  args: { context: TContext; event: TEvent },
  params?: any,
) => boolean;

export const isUnderAge: GuardFunction = ({ event }) => {
  if ((event as any).type !== 'next' || !(event as any).value) return false;
  const data = (event as any).value as QuestionnaireStepData;
  if (!data.birthDate) return false;
  const birthDate = dayjs(data.birthDate);
  const eighteenYearsAgo = dayjs().subtract(18, 'year');

  return birthDate.isAfter(eighteenYearsAgo);
};

export const isOverAge: GuardFunction = ({ event }) => {
  if ((event as any).type !== 'next' || !(event as any).value) return false;
  const data = (event as any).value as QuestionnaireStepData;
  if (!data.birthDate) return false;
  const birthDate = dayjs(data.birthDate);
  const seventyFiveYearsAgo = dayjs().subtract(75, 'year');

  return birthDate.isBefore(seventyFiveYearsAgo);
};

export const verifyPriorConditions: GuardFunction = ({ event }) => {
  if ((event as any).type !== 'next' || !(event as any).value) return false;
  const data = (event as any).value as QuestionnaireStepData;
  if (!data.priorConditions) return false;
  return data.priorConditions.length === 0;
};

export const hasSsnSuccess: GuardFunction = ({ context }) => {
  return !!(context as any).ssnVerified && !!(context as any).ssnVerificationId;
};

export const hasSsnCheckFailed: GuardFunction = ({ context }) => {
  return (context as any).ssnCheckFailed === true;
};
