import type { OnboardingLegacyV1Input } from './types/onboarding-legacy-v1.types';
import type { OnboardingLegacyV2Input } from './types/onboarding-legacy-v2.types';
import type { OnboardingV1Input } from './types/onboarding-v1.types';
import type { OnboardingV2Input } from './types/onboarding-v2.types';
import { onboardingLegacyV1Machine } from './onboarding-legacy-v1.state';
import { onboardingLegacyV2Machine } from './onboarding-legacy-v2.state';
import { onboardingV1Machine } from './onboarding-v1.state';
import { onboardingV2Machine } from './onboarding-v2.state';

/**
 * Machine registry that maps versions to their state machines
 * Adding new versions is as simple as adding a new entry here
 */
export const ONBOARDING_MACHINE_REGISTRY = {
  v1: onboardingV1Machine,
  v2: onboardingV2Machine,
  'legacy-v1': onboardingLegacyV1Machine,
  'legacy-v2': onboardingLegacyV2Machine,
} as const;

/**
 * Derive the version type from the registry keys
 * This ensures OnboardingVersion is always in sync with available machines
 */
export type OnboardingVersion = keyof typeof ONBOARDING_MACHINE_REGISTRY;

/**
 * Union type of all possible machine inputs
 * Use this for type-safe machine initialization
 */
export type AnyOnboardingInput =
  | OnboardingV1Input
  | OnboardingV2Input
  | OnboardingLegacyV1Input
  | OnboardingLegacyV2Input;

/**
 * Get the default onboarding version for new patients
 * This function can be enhanced to support:
 * - A/B testing based on user attributes
 * - Feature flags
 * - Time-based rollouts
 */
export function getDefaultOnboardingVersion(): OnboardingVersion {
  return 'v2';
}
