import { assign, setup } from 'xstate';

import type {
  OnboardingLegacyV1Context,
  OnboardingLegacyV1Events,
  OnboardingLegacyV1Input,
  PostQuestionnaireData,
  QuestionnaireStepData,
} from './types/onboarding-legacy-v1.types';
import { QUESTIONNAIRE_QUESTIONS } from '../questionnaire-questions';
import * as guards from './guards';

export const onboardingLegacyV1Machine = setup({
  types: {
    context: {} as OnboardingLegacyV1Context,
    events: {} as OnboardingLegacyV1Events,
    input: {} as OnboardingLegacyV1Input,
  },
  guards: { ...guards },
  actions: {
    reject: assign((_, params: { reason: string }) => ({
      rejected: true,
      rejectedReason: params.reason,
    })),
    clearRejected: assign(() => {
      return { rejected: false, rejectedReason: undefined };
    }),
    complete: assign(() => {
      return { questionnaireCompleted: true };
    }),
    storeQuestionnaire: assign(({ context, event }, params) => {
      // For 'next' events, merge the event value data
      const data =
        event.type === 'next' && event.value
          ? (event.value as QuestionnaireStepData)
          : {};

      // Always merge params (for radio button selections) and data
      context.questionnaire = {
        ...context.questionnaire,
        ...data,
        ...params,
      };
      return context;
    }),
    store: assign(({ context, event }) => {
      if (event.type !== 'next') return context;
      const data = event.value as PostQuestionnaireData;
      return { ...context, ...data };
    }),
    storeClientSecret: assign(({ context, event }) => {
      if (event.type !== 'next' || !event.value) return context;
      const data = event.value as PostQuestionnaireData;
      if (!data.paymentIntent) return context;
      return { ...context, paymentIntent: data.paymentIntent };
    }),
  },
}).createMachine({
  context: ({ input }) => ({
    questionnaireCompleted: false,
    questionnaire: {},
    ...(input?.existingData || {}),
  }),
  id: 'onboarding-legacy-v1',
  initial: 'questionnaire',
  meta: {
    version: 'legacy-v1',
    total: 15 + 9, // 15 questionnaire + 9 post-questionnaire
  },
  states: {
    questionnaire: {
      meta: { total: 15 },
      initial: 'age',
      states: {
        age: {
          on: {
            next: [
              { guard: 'isUnderAge', target: 'rejectedUnderAge' },
              { guard: 'isOverAge', target: 'rejectedOverAge' },
              { target: 'gender', actions: 'storeQuestionnaire' },
            ],
          },
          meta: { step: 5, question: QUESTIONNAIRE_QUESTIONS.birthDate },
        },
        rejectedUnderAge: {
          entry: { type: 'reject', params: { reason: 'under age' } },
          on: { back: { target: 'age', actions: 'clearRejected' } },
          meta: { step: 5 },
        },
        rejectedOverAge: {
          entry: { type: 'reject', params: { reason: 'over age' } },
          on: { back: { target: 'age', actions: 'clearRejected' } },
          meta: { step: 5 },
        },
        gender: {
          on: {
            back: { target: 'age' },
            male: {
              target: 'usingGLP1',
              actions: {
                type: 'storeQuestionnaire',
                params: { gender: 'male' },
              },
            },
            female: {
              target: 'isPregnant',
              actions: {
                type: 'storeQuestionnaire',
                params: { gender: 'female' },
              },
            },
          },
          meta: { step: 7, question: QUESTIONNAIRE_QUESTIONS.gender },
        },
        isPregnant: {
          on: {
            no: {
              target: 'usingGLP1',
              actions: {
                type: 'storeQuestionnaire',
                params: { isPregnant: 'no' },
              },
            },
            yes: {
              target: 'rejectedIsPregnant',
              actions: {
                type: 'storeQuestionnaire',
                params: { isPregnant: 'yes' },
              },
            },
            back: { target: 'gender' },
          },
          meta: { step: 7, question: QUESTIONNAIRE_QUESTIONS.isPregnant },
        },
        rejectedIsPregnant: {
          entry: { type: 'reject', params: { reason: 'is pregnant' } },
          on: {
            back: {
              target: 'isPregnant',
              actions: 'clearRejected',
            },
          },
          meta: {
            step: 7,
          },
        },
        usingGLP1: {
          on: {
            no: {
              target: 'haveDiabetes',
              actions: {
                type: 'storeQuestionnaire',
                params: { usingGLP1: 'no' },
              },
            },
            yes: {
              target: 'haveDiabetes',
              actions: {
                type: 'storeQuestionnaire',
                params: { usingGLP1: 'yes' },
              },
            },
            back: { target: 'gender' },
          },
          meta: { step: 7, question: QUESTIONNAIRE_QUESTIONS.usingGLP1 },
        },
        haveDiabetes: {
          on: {
            no: {
              target: 'eligible',
              actions: {
                type: 'storeQuestionnaire',
                params: { haveDiabetes: 'no' },
              },
            },
            yes: {
              target: 'rejectedPriorConditions',
              actions: {
                type: 'storeQuestionnaire',
                params: { haveDiabetes: 'yes' },
              },
            },
            back: { target: 'usingGLP1' },
          },
          meta: { step: 7, question: QUESTIONNAIRE_QUESTIONS.haveDiabetes },
        },
        rejectedPriorConditions: {
          entry: { type: 'reject', params: { reason: 'has prior conditions' } },
          on: {
            back: { target: 'haveDiabetes', actions: 'clearRejected' },
          },
          meta: { step: 7 },
        },
        eligible: {
          on: {
            back: { target: 'haveDiabetes' },
            next: { target: 'doctorVisits' },
          },
          meta: { step: 8 },
        },
        doctorVisits: {
          on: {
            no: {
              target: 'recommendSeeDoctor',
              actions: {
                type: 'storeQuestionnaire',
                params: { doctorVisits: 'no' },
              },
            },
            yes: {
              target: 'qualifyingConditions',
              actions: {
                type: 'storeQuestionnaire',
                params: { doctorVisits: 'yes' },
              },
            },
            back: { target: 'eligible' },
          },
          meta: { step: 9, question: QUESTIONNAIRE_QUESTIONS.doctorVisits },
        },
        recommendSeeDoctor: {
          on: {
            back: { target: 'doctorVisits' },
            next: { target: 'qualifyingConditions' },
          },
          meta: { step: 9 },
        },
        qualifyingConditions: {
          on: {
            back: { target: 'doctorVisits' },
            next: { target: 'height', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 10,
            question: QUESTIONNAIRE_QUESTIONS.qualifyingConditions,
          },
        },
        height: {
          on: {
            back: { target: 'qualifyingConditions' },
            next: { target: 'weight', actions: 'storeQuestionnaire' },
          },
          meta: { step: 11, question: QUESTIONNAIRE_QUESTIONS.height },
        },
        weight: {
          on: {
            back: { target: 'height' },
            next: { target: 'desiredWeight', actions: 'storeQuestionnaire' },
          },
          meta: { step: 12, question: QUESTIONNAIRE_QUESTIONS.weight },
        },
        desiredWeight: {
          on: {
            back: { target: 'weight' },
            next: { target: 'haveAllergies', actions: 'storeQuestionnaire' },
          },
          meta: { step: 13, question: QUESTIONNAIRE_QUESTIONS.desiredWeight },
        },
        haveAllergies: {
          on: {
            no: {
              target: 'medications',
              actions: {
                type: 'storeQuestionnaire',
                params: { allergies: [], hasAllergies: 'no' },
              },
            },
            yes: {
              target: 'selectAllergies',
              actions: {
                type: 'storeQuestionnaire',
                params: { hasAllergies: 'yes' },
              },
            },
            back: { target: 'desiredWeight' },
          },
          meta: { step: 15, question: QUESTIONNAIRE_QUESTIONS.hasAllergies },
        },
        medications: {
          on: {
            back: { target: 'haveAllergies' },
            next: {
              target: 'medicalConditions',
              actions: 'storeQuestionnaire',
            },
          },
          meta: { step: 16, question: QUESTIONNAIRE_QUESTIONS.medications },
        },
        selectAllergies: {
          on: {
            back: { target: 'haveAllergies' },
            next: { target: 'medications', actions: 'storeQuestionnaire' },
          },
          meta: { step: 15, question: QUESTIONNAIRE_QUESTIONS.allergies },
        },
        medicalConditions: {
          on: {
            back: { target: 'medications' },
            next: {
              target: 'additionalInformation',
              actions: 'storeQuestionnaire',
            },
          },
          meta: {
            step: 17,
            question: QUESTIONNAIRE_QUESTIONS.medicalConditions,
          },
        },
        additionalInformation: {
          on: {
            back: { target: 'medicalConditions' },
            next: { target: 'finished', actions: 'storeQuestionnaire' },
          },
          meta: {
            step: 18,
            question: QUESTIONNAIRE_QUESTIONS.additionalInformation,
          },
        },
        finished: {
          entry: { type: 'complete', params: { completed: true } },
          always: '#onboarding-legacy-v1.selectTreatmentType',
        },
      },
    },
    selectTreatmentType: {
      on: {
        next: { target: 'selectTreatment', actions: 'store' },
        back: { target: 'questionnaire.additionalInformation' },
      },
      meta: { step: 1, name: 'Virtual Doctor Visit' },
    },
    selectTreatment: {
      on: {
        next: { target: 'info', actions: 'store' },
        back: { target: 'selectTreatmentType' },
      },
      meta: { step: 2, name: 'Virtual Doctor Visit' },
    },
    info: {
      on: {
        next: { target: 'uploadIDPhoto' },
        back: { target: 'selectTreatment' },
      },
      meta: { step: 3, name: 'Virtual Doctor Visit' },
    },
    uploadIDPhoto: {
      on: {
        next: { target: 'uploadFacePhoto', actions: 'store' },
        back: { target: 'info' },
      },
      meta: { step: 4, name: 'Virtual Doctor Visit' },
    },
    uploadFacePhoto: {
      on: {
        next: { target: 'visitCompletion', actions: 'store' },
        back: { target: 'uploadIDPhoto' },
      },
      meta: { step: 5, name: 'Virtual Doctor Visit' },
    },
    visitCompletion: {
      on: {
        next: { target: 'summary' },
        back: { target: 'uploadFacePhoto' },
      },
      meta: { step: 6, name: 'Checkout' },
    },
    summary: {
      on: {
        next: { target: 'shipping' },
        back: { target: 'visitCompletion' },
      },
      meta: { step: 7, name: 'Checkout' },
    },
    shipping: {
      on: {
        next: { target: 'payment', actions: 'store' },
        back: { target: 'summary' },
      },
      meta: { step: 8, name: 'Checkout' },
    },
    payment: {
      on: {
        update: { actions: 'storeClientSecret' },
        complete: { target: 'onboarded' },
        back: { target: 'shipping' },
      },
      meta: { step: 9, name: 'Checkout' },
    },
    onboarded: {
      type: 'final',
      meta: { step: 10 },
    },
  },
});
