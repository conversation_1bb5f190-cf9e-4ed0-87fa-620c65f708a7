import type { ShippingInfoDto } from '@modules/onboarding/dto/shipping-info.dto';
import type { DesiredTreatment } from '@modules/onboarding/use-cases/onboarding-desired-treatment.use-case';

// Questionnaire step data types
export interface QuestionnaireStepData {
  birthDate?: string;
  gender?: 'male' | 'female';
  height?: number;
  weight?: number;
  desiredWeight?: number;
  usingGLP1?: 'yes' | 'no';
  isPregnant?: 'yes' | 'no';
  haveDiabetes?: 'yes' | 'no';
  doctorVisits?: 'yes' | 'no';
  hasAllergies?: 'yes' | 'no';
  allergies?: string[];
  medications?: string[];
  objectives?: string[];
  medicalConditions?: string[];
  qualifyingConditions?: string[];
  additionalInformation?: string;
  priorConditions?: string[];
}

// Post-questionnaire data types
export interface PostQuestionnaireData {
  productType?: string;
  products?: DesiredTreatment[];
  'id-photo'?: string;
  'face-photo'?: string;
  shippingInfo?: ShippingInfoDto;
  paymentIntent?: string;
}

// Complete context type
export interface OnboardingLegacyV2Context {
  // State management
  rejected?: boolean;
  questionnaireCompleted: boolean;
  rejectedReason?: string;

  // Questionnaire data (structured)
  questionnaire: {
    birthDate?: string;
    gender?: 'male' | 'female';
    height?: number;
    weight?: number;
    desiredWeight?: number;
    usingGLP1?: 'yes' | 'no';
    isPregnant?: 'yes' | 'no';
    haveDiabetes?: 'yes' | 'no';
    doctorVisits?: 'yes' | 'no';
    hasAllergies?: 'yes' | 'no';
    allergies?: string[];
    medications?: string[];
    objectives?: string[];
    medicalConditions?: string[];
    qualifyingConditions?: string[];
    additionalInformation?: string;
  };

  // Post-questionnaire data
  pharmacyId?: string;
  productType?: string;
  products?: DesiredTreatment[];
  'id-photo'?: string;
  'face-photo'?: string;
  shippingInfo?: ShippingInfoDto;
  paymentIntent?: string;
}

// Event types with proper typing
export type OnboardingLegacyV2Events =
  // Navigation events
  | {
      type: 'next';
      value?: QuestionnaireStepData | PostQuestionnaireData;
    }
  | { type: 'back' }

  // Questionnaire specific answer events
  | { type: 'male' }
  | { type: 'female' }
  | { type: 'yes' }
  | { type: 'no' };

// Input type for machine initialization
export interface OnboardingLegacyV2Input {
  version: 'legacy-v2';
  existingData?: Partial<OnboardingLegacyV2Context>;
}
