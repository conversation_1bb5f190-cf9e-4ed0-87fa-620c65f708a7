import { OnboardingPersistence } from '@adapters/persistence/database';
import { AppCacheModule } from '@modules/cache/cache.module';
import { OnboardingCookieService } from '@modules/onboarding/services/onboarding-cookie.service';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { PatientOnboardingService } from '@modules/onboarding/services/patient-onboarding.service';
import { OnboardingUseCases } from '@modules/onboarding/use-cases';
import { OnboardingCompleteUseCase } from '@modules/onboarding/use-cases/onboarding-complete.use-case';
import { OnboardingInitializeUseCase } from '@modules/onboarding/use-cases/onboarding-initialize.use-case';
import { PatientAddToWaitingListUseCase } from '@modules/patient/use-cases/patient-add-to-waiting-list-use.case';
import { PatientGetPreSignedUrlUseCase } from '@modules/patient/use-cases/patient-get-pre-signed-url.use-case';
import { PatientSignInUseCase } from '@modules/patient/use-cases/patient-sign-in-use.case';
import { PatientSignUpUseCase } from '@modules/patient/use-cases/patient-sign-up.use-case';
import { PharmacyModule } from '@modules/pharmacy/pharmacy.module';
import { UtmService } from '@modules/shared/services/utm.service';
import { SharedModule } from '@modules/shared/shared.module';
import { StripeModule } from '@modules/stripe/stripe.module';
import { TreatmentModule } from '@modules/treatment/treatment.module';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AuditLogModule } from '../audit-log/audit-log.module';
import { AuthModule } from '../auth/auth.module';
import { ContextModule } from '../context/context.module';
import { PrismaModule } from '../prisma/prisma.module';
import { ReferralModule } from '../referral/referral.module';
import { OnboardingPublicOnlyGuard } from './guards/onboarding-public-only.guard';
import { OnboardingPublicController } from './onboarding-public.controller';
import { OnboardingController } from './onboarding.controller';
import { OnboardingEventEmitterService } from './use-cases/onboarding-event-emitter.service';

@Module({
  imports: [
    ConfigModule,
    SharedModule,
    PrismaModule,
    StripeModule,
    AuditLogModule,
    forwardRef(() => AuthModule),
    ReferralModule,
    PharmacyModule,
    forwardRef(() => TreatmentModule),
    AppCacheModule,
    ContextModule,
  ],
  controllers: [OnboardingController, OnboardingPublicController],
  providers: [
    PatientOnboardingService,
    OnboardingStateService,
    OnboardingCookieService,
    OnboardingPublicOnlyGuard,
    OnboardingInitializeUseCase,
    PatientGetPreSignedUrlUseCase,
    PatientSignInUseCase,
    PatientSignUpUseCase,
    PatientAddToWaitingListUseCase,
    ...OnboardingUseCases,
    ...OnboardingPersistence,
    OnboardingEventEmitterService,
    UtmService,
  ],
  exports: [
    OnboardingStateService,
    OnboardingCookieService,
    OnboardingInitializeUseCase,
    OnboardingCompleteUseCase,
    OnboardingEventEmitterService,
  ],
})
export class OnboardingModule {}
