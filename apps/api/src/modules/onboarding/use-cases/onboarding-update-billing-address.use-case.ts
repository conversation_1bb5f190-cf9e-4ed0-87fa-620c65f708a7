import { PatientOnboardingProfile } from '@adapters/persistence/database/patient.persistence';
import { ShippingInfoDto } from '@modules/onboarding/dto/shipping-info.dto';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { TaxjarService } from '@modules/shared/services/taxjar.service';
import { StripeService } from '@modules/stripe/service/stripe.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class OnboardingUpdateBillingAddressUseCase {
  constructor(
    private readonly stripeService: StripeService,
    private readonly onboardingService: OnboardingStateService,
    private readonly taxjarService: TaxjarService,
  ) {}

  async execute(
    profile: PatientOnboardingProfile,
    billingAddress: ShippingInfoDto,
  ) {
    // check current onboarding state
    const onboarding = await this.onboardingService.getCurrentOnboardingActor(
      profile.onboardingVersion as OnboardingVersion,
      profile.onboardingState as AnyOnboardingSnapshot,
    );
    const snapshot = onboarding.getSnapshot();
    if (snapshot.value !== 'payment') {
      throw new Error('Invalid onboarding state');
    }

    // create stripe customer if not exists
    const stripeCustomerId = profile.stripeCustomerId;
    if (!stripeCustomerId) {
      throw new Error('Stripe customer not found');
    }
    const {
      address1: line1,
      address2: line2,
      city,
      state,
      zip: postal_code,
    } = billingAddress;

    // validate address - will throw appropriate errors if validation fails
    await this.taxjarService.validateAddress(billingAddress);

    await this.stripeService.updateBillingAddress(stripeCustomerId, {
      line1,
      line2,
      city,
      state,
      postal_code,
    });

    return this.onboardingService.getCurrentOnboardingState(
      profile.onboardingVersion as OnboardingVersion,
      snapshot,
    );
  }
}
