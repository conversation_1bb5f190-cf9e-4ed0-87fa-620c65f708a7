import { OnboardingEventEmitterService } from '@/modules/onboarding/use-cases/onboarding-event-emitter.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { segmentIdentifyEvent } from '@/modules/shared/events';
import { SegmentIdentify } from '@/modules/shared/types/events';
import { PatientPaymentMethodPersistence } from '@adapters/persistence/database/patient-payment-method.persistence';
import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { ShippingAddressPersistence } from '@adapters/persistence/database/shipping-address.persistence';
import { StatePersistence } from '@adapters/persistence/database/state.persistence';
import {
  OnboardingActor,
  OnboardingStateService,
} from '@modules/onboarding/services/onboarding-state.service';
import { ShippingInfo } from '@modules/onboarding/types/shipping-info.type';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { patientGender, patientStatus, Prisma } from '@prisma/client';

@Injectable()
export class OnboardingCompleteUseCase {
  constructor(
    private readonly patientPersistence: PatientPersistence,
    private readonly statePersistence: StatePersistence,
    private readonly shippingAddressPersistence: ShippingAddressPersistence,
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPaymentMethodPersistence: PatientPaymentMethodPersistence,
    private readonly prismaService: PrismaService,
    private readonly onboardingEventEmitterService: OnboardingEventEmitterService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async execute(
    profile: PatientOnboardingProfile,
    onboarding: OnboardingActor,
    paymentMethodId: string,
    paymentMethodData: any,
    paymentMethodType: string,
  ) {
    try {
      await this.prismaService.$transaction(async (prisma) => {
        // perform complete transition and get context data
        const actor = this.onboardingService.performTransition(
          onboarding,
          'complete',
        );
        const snapshot = actor.getSnapshot();
        const context = snapshot.context;

        // persist desired products
        const desiredTreatments = context.products.map((product) => ({
          productId: product.id,
          vials: 1,
        }));
        await this.patientPersistence.update(
          profile.id,
          {
            desiredTreatments: {
              create: desiredTreatments,
            },
          },
          prisma,
        );

        const state = await this.statePersistence.getByCode(
          context.shippingInfo.state,
        );
        const shippingStateChanged = state.id !== profile.state.id;

        // persist shipping address
        const shipping: ShippingInfo = {
          address1: context.shippingInfo.address1,
          address2: context.shippingInfo.address2,
          city: context.shippingInfo.city,
          stateId: state.id,
          zip: context.shippingInfo.zip.toString(),
        };
        await this.shippingAddressPersistence.create(
          profile.id,
          shipping,
          prisma,
        );

        // persist payment method
        await this.patientPaymentMethodPersistence.create({
          patient: {
            connect: { id: profile.id },
          },
          stripeId: paymentMethodId,
          data: paymentMethodData,
          type: paymentMethodType,
          default: true,
        });

        // update patient profile
        const questionnaire = context.questionnaire;
        const birthDate = new Date(questionnaire['birthDate']);
        const gender = questionnaire['gender'] as patientGender;
        const height = questionnaire['height'];
        const weight = questionnaire['weight'];
        const idPhoto = context['id-photo'];
        const facePhoto = context['face-photo'];

        // Determine if this is successful document verification
        const isDocumentVerified = context.ssnVerified === true;

        // Determine status based on verification type
        const status: patientStatus = isDocumentVerified
          ? facePhoto
            ? 'onboardingCompleted'
            : 'pendingUploadPhotos'
          : idPhoto && facePhoto
            ? 'onboardingCompleted'
            : 'pendingUploadPhotos';

        // Build update data including pharmacy if override exists and state if changed
        const updateData: Omit<Prisma.PatientUpdateInput, 'onboardingState'> = {
          status,
          birthDate,
          gender,
          height,
          weight,
          idPhoto,
          facePhoto,
          completedAt: new Date(),
        };

        // Set identity verification type if we're in a flow that has SSN verification context
        // (either succeeded, failed, or skipped)
        if ('ssnVerified' in context || 'ssnCheckFailed' in context) {
          // Only 'document' if SSN verification succeeded, otherwise 'photoId'
          updateData.identityVerificationType = isDocumentVerified
            ? 'document'
            : 'photoId';

          // Only add SSN-related fields if document verification succeeded
          if (isDocumentVerified) {
            updateData.lastFourSSN = context.lastFourSSN;
            updateData.vouchedVerificationId = context.ssnVerificationId;
            updateData.vouchedVerifiedAt = context.vouchedVerifiedAt;
          }
        }

        // Add pharmacy update if override exists in context
        if (context.pharmacyId && context.pharmacyId !== profile.pharmacy?.id) {
          Object.assign(updateData, {
            pharmacy: { connect: { id: context.pharmacyId } },
          });
        }

        if (shippingStateChanged) {
          Object.assign(updateData, { state: { connect: { id: state.id } } });

          // Emit segment identify event for state change
          const identifyEvent: SegmentIdentify = {
            userId: profile.id,
            traits: { state: context.shippingInfo.state },
          };
          this.eventEmitter.emit(
            segmentIdentifyEvent.analyticIdentify,
            identifyEvent,
          );
        }

        await this.patientPersistence.updateOnboarding(
          profile.id,
          onboarding.getSnapshot(),
          updateData,
          prisma,
        );

        void this.onboardingEventEmitterService.execute(profile, snapshot);
      });
    } catch (e) {
      console.error(e);
      throw e;
    }
  }
}
