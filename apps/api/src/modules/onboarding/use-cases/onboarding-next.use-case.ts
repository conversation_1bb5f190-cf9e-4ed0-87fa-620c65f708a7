import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { Injectable } from '@nestjs/common';

import { OnboardingEventEmitterService } from './onboarding-event-emitter.service';

@Injectable()
export class OnboardingNextUseCase {
  constructor(
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPersistence: PatientPersistence,
    private readonly onBoardingEventEmitter: OnboardingEventEmitterService,
  ) {}

  async execute(profile: PatientOnboardingProfile) {
    if (profile.status !== 'onboardingPending') {
      throw new Error('Invalid onboarding state');
    }

    const onboarding = await this.onboardingService.getCurrentOnboardingActor(
      profile.onboardingVersion as OnboardingVersion,
      profile.onboardingState as AnyOnboardingSnapshot,
    );

    // perform transition and get new state
    const result = this.onboardingService.performTransition(onboarding, 'next');

    // persist new questionnaire state
    const snapshot = result.getSnapshot();
    await this.patientPersistence.updateOnboarding(profile.id, snapshot);
    this.onBoardingEventEmitter.execute(profile, snapshot);
    return this.onboardingService.getCurrentOnboardingState(
      profile.onboardingVersion as OnboardingVersion,
      snapshot,
    );
  }
}
