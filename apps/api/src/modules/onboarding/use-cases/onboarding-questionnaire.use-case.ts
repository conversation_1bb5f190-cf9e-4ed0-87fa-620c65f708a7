import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { QuestionnaireInput } from '@modules/onboarding/types/onboarding.type';
import { OnboardingEventEmitterService } from '@modules/onboarding/use-cases/onboarding-event-emitter.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class OnboardingQuestionnaireUseCase {
  constructor(
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPersistence: PatientPersistence,
    private readonly onboardingEventEmitterService: OnboardingEventEmitterService,
  ) {}
  async execute(profile: PatientOnboardingProfile, data: QuestionnaireInput) {
    //validate it's on onboarding state
    const onboarding = await this.onboardingService.getCurrentOnboardingActor(
      profile.onboardingVersion as OnboardingVersion,
      profile.onboardingState as AnyOnboardingSnapshot,
    );
    const state = onboarding.getSnapshot();
    if (!state.value['questionnaire']) {
      throw new Error('Invalid onboarding state');
    }

    // perform transition and get new state
    const result = this.onboardingService.performTransition(
      onboarding,
      data.event,
      data.value,
    );

    // persist new questionnaire state
    const snapshot = result.getSnapshot();
    await this.patientPersistence.updateOnboarding(profile.id, snapshot);

    void this.onboardingEventEmitterService.processQuestionnaireStepIdentifyEvents(
      profile,
      state, // old state before transition
      snapshot, // new state after transition
    );
    // update if rejected
    if (snapshot.context.rejected) {
      await this.patientPersistence.rejectOnboarding(
        profile.id,
        snapshot,
        snapshot.context.rejectedReason,
      );
    }

    return this.onboardingService.getCurrentOnboardingState(
      profile.onboardingVersion as OnboardingVersion,
      snapshot,
    );
  }
}
