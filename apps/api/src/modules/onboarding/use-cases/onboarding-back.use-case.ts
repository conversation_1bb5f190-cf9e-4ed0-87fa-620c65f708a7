import {
  PatientOnboardingProfile,
  PatientPersistence,
} from '@adapters/persistence/database/patient.persistence';
import { OnboardingStateService } from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { AnyOnboardingSnapshot } from '@modules/onboarding/types/cookie.types';
import { Injectable } from '@nestjs/common';

@Injectable()
export class OnboardingBackUseCase {
  constructor(
    private readonly onboardingService: OnboardingStateService,
    private readonly patientPersistence: PatientPersistence,
  ) {}

  async execute(profile: PatientOnboardingProfile) {
    if (!['onboardingPending', 'onboardingRejected'].includes(profile.status)) {
      throw new Error('Invalid onboarding state');
    }

    const onboardingActor =
      await this.onboardingService.getCurrentOnboardingActor(
        profile.onboardingVersion as OnboardingVersion,
        profile.onboardingState as AnyOnboardingSnapshot,
      );
    const beforeTransitionSnapshot = onboardingActor.getSnapshot();

    // perform transition and get new state
    const resultActor = this.onboardingService.performTransition(
      onboardingActor,
      'back',
    );

    // persist new questionnaire state
    const afterTransitionSnapshot = resultActor.getSnapshot();
    await this.patientPersistence.updateOnboarding(
      profile.id,
      afterTransitionSnapshot,
    );
    const rejectionCleared =
      beforeTransitionSnapshot.context.rejected == true &&
      afterTransitionSnapshot.context.rejected == false;
    if (rejectionCleared) {
      await this.patientPersistence.updateOnboarding(
        profile.id,
        afterTransitionSnapshot,
        {
          status: 'onboardingPending',
          rejectedReason: null,
        },
      );
    }

    return this.onboardingService.getCurrentOnboardingState(
      profile.onboardingVersion as OnboardingVersion,
      afterTransitionSnapshot,
    );
  }
}
