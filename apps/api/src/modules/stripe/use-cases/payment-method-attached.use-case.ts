import { PatientPersistence } from '@adapters/persistence/database/patient.persistence';
import {
  OnboardingSnapshot,
  OnboardingStateService,
} from '@modules/onboarding/services/onboarding-state.service';
import { OnboardingVersion } from '@modules/onboarding/states/versions';
import { OnboardingCompleteUseCase } from '@modules/onboarding/use-cases/onboarding-complete.use-case';
import { Injectable } from '@nestjs/common';
import { Stripe } from 'stripe';

type PatientOboardingProfile = Awaited<
  ReturnType<PatientPersistence['getOnboardingProfileByStripeCustomer']>
>;

@Injectable()
export class StripeAttemptOnboadingPaymentValidationUseCase {
  constructor(
    private readonly patientPersistence: PatientPersistence,
    private readonly onboardingService: OnboardingStateService,
    private readonly onboardingCompleteUseCase: OnboardingCompleteUseCase,
  ) {}

  async execute(event: Stripe.PaymentMethodAttachedEvent) {
    let profile: PatientOboardingProfile;

    try {
      profile =
        await this.patientPersistence.getOnboardingProfileByStripeCustomer(
          event.data.object.customer as string,
        );
    } catch (error) {
      console.error('Error fetching patient profile:', error);
      return;
    }

    // if user aleady completed onboarding, skip silently
    if (profile.status !== 'onboardingPending') return;

    try {
      const onboarding = await this.onboardingService.getCurrentOnboardingActor(
        profile.onboardingVersion as OnboardingVersion,
        profile.onboardingState as unknown as OnboardingSnapshot,
      );
      const snapshot = onboarding.getSnapshot();

      if (snapshot.value !== 'payment') {
        throw new Error('Invalid onboarding state');
      }

      await this.onboardingCompleteUseCase.execute(
        profile,
        onboarding,
        event.data.object.id,
        event.data.object as any,
        event.data.object.type,
      );
    } catch (error) {
      console.error('Error fetching patient profile:', error);
      throw new Error(
        `Failed to handle payment method attached for patient ${profile.id}: ${error.message}`,
      );
    }
  }
}
