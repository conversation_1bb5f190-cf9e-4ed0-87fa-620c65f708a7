import { runInDbTransaction } from '@/helpers/transaction';
import { AuditService } from '@/modules/audit-log/audit-log.service';
import { PrismaService } from '@/modules/prisma/prisma.service';
import { segmentIdentifyEvent } from '@/modules/shared/events';
import { SegmentIdentify } from '@/modules/shared/types/events';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import Stripe from 'stripe';

@Injectable()
export class CustomerDefaultPaymentMethodUpdatedUseCase {
  private readonly logger = new Logger(
    CustomerDefaultPaymentMethodUpdatedUseCase.name,
  );

  constructor(
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly auditService: AuditService,
  ) {}
  async execute(patientId, customer: Stripe.Customer) {
    const patient = await this.prismaService.patient.findFirst({
      where: { id: patientId },
      select: {
        id: true,
        userId: true,
        stripeCustomerId: true,
        paymentMethods: {
          take: 1,
          where: {
            default: true,
          },
        },
      },
    });

    if (!patient || !patient.stripeCustomerId) {
      this.logger.error(
        `Patient with ID ${patientId} not found or does not have a Stripe customer ID.`,
      );
      throw new Error('Patient not found');
    }

    const paymentMethodId = customer.invoice_settings
      ?.default_payment_method as string;
    if (!paymentMethodId) {
      // default payment method removed from stripe
      return await runInDbTransaction(this.prismaService, async (prisma) => {
        await prisma.patientPaymentMethod.updateMany({
          where: {
            patientId,
          },
          data: {
            default: false,
          },
        });

        let oldPaymentMethod: { data: Stripe.PaymentMethod } | undefined =
          undefined;
        if (patient.paymentMethods.length > 0) {
          oldPaymentMethod = patient.paymentMethods[0]! as {
            data: Stripe.PaymentMethod;
          };
        }

        const sourceUpdatedIdentifyEvent: SegmentIdentify = {
          userId: patient.id,
          traits: {
            cardExpMonth: null,
            cardExpYear: null,
            cardLast4: null,
            cardExpirationTime: null,
            cardType: null,
          },
        };

        await this.auditService.append(
          {
            patientId: patient.id,
            resourceType: 'PATIENT',
            resourceId: patient.id,
            action: 'PATIENT_PAYMENT_INFO_UPDATED',
            actorType: 'SYSTEM',
            actorId: 'STRIPE',
            details: {
              type: '',
              old: {
                cardLast4: oldPaymentMethod.data?.card?.last4 ?? '',
                cardType: oldPaymentMethod.data?.card?.brand,
                cardExpMonth: String(oldPaymentMethod?.data?.card?.exp_month),
                cardExpYear: String(oldPaymentMethod?.data?.card?.exp_year),
              },
              new: null,
            },
          },
          { prisma },
        );

        this.eventEmitter.emit(
          segmentIdentifyEvent.analyticIdentify,
          sourceUpdatedIdentifyEvent,
        );
      });
    }

    await runInDbTransaction(this.prismaService, async (prisma) => {
      await prisma.patientPaymentMethod.updateMany({
        where: {
          patientId,
        },
        data: {
          default: false,
        },
      });

      const card = await prisma.patientPaymentMethod.update({
        where: {
          patientId: patient.id,
          stripeId: paymentMethodId,
        },
        data: {
          default: true,
        },
      });

      const paymentMethod = card.data as Stripe.PaymentMethod;

      let oldPaymentMethod: { data: Stripe.PaymentMethod } | undefined =
        undefined;
      if (patient.paymentMethods.length > 0) {
        oldPaymentMethod = patient.paymentMethods[0]! as {
          data: Stripe.PaymentMethod;
        };
      }

      const sourceUpdatedIdentifyEvent: SegmentIdentify = {
        userId: patient.id,
        traits: {
          cardExpMonth: String(paymentMethod.card.exp_month),
          cardExpYear: String(paymentMethod.card.exp_year),
          cardLast4: paymentMethod.card.last4,
          cardExpirationTime: new Date(
            paymentMethod.card.exp_year,
            paymentMethod.card.exp_month + 1,
            0,
          ).getTime(),
          cardType: paymentMethod.card.brand,
        },
      };

      await this.auditService.append(
        {
          patientId: patient.id,
          resourceType: 'PATIENT',
          resourceId: patient.id,
          action: 'PATIENT_PAYMENT_INFO_UPDATED',
          actorType: 'SYSTEM',
          actorId: 'STRIPE',
          details: {
            type: paymentMethod.type,
            old: {
              cardLast4: oldPaymentMethod.data?.card?.last4 ?? '',
              cardType: oldPaymentMethod.data?.card?.brand,
              cardExpMonth: String(oldPaymentMethod?.data?.card?.exp_month),
              cardExpYear: String(oldPaymentMethod?.data?.card?.exp_year),
            },
            new:
              paymentMethod.type === 'card'
                ? {
                    cardLast4: paymentMethod.card.last4,
                    cardType: paymentMethod.card.brand,
                    cardExpMonth: String(paymentMethod.card.exp_month),
                    cardExpYear: String(paymentMethod.card.exp_year),
                  }
                : (paymentMethod as any),
          },
        },
        { prisma },
      );

      this.eventEmitter.emit(
        segmentIdentifyEvent.analyticIdentify,
        sourceUpdatedIdentifyEvent,
      );
    });
  }
}
