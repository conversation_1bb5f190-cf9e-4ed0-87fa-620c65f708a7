import * as fs from 'fs';
import * as path from 'path';
import type {
  ConversationHistoryWithRouting,
  MessageData,
} from '@modules/chat/services/patient-message-router.service';
import { input } from '@inquirer/prompts';
import search from '@inquirer/search';
import { BedrockModel } from '@modules/ai/ai.service';
import { PatientMessageRouterService } from '@modules/chat/services/patient-message-router.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import * as chalk from 'chalk';
import { Command, CommandRunner, Option } from 'nest-commander';
import { table } from 'table';
import { v4 as uuidv4 } from 'uuid';

interface SimulatedRouter {
  id: string;
  messages: string[];
  status: 'processed' | 'closed';
  relevantForDoctor: boolean;
  relevantForPatientServices: boolean;
  reason: string;
  inquiryTypes: string[];
  intercomId: string | null;
  processedAt: Date;
  continuationConfidence?: number;
  analysisSummary?: string;
}

interface PreviousRouting {
  id: string;
  status: 'processed' | 'closed';
  relevantForDoctor: boolean;
  relevantForPatientServices: boolean;
  reason: string;
  inquiryTypes: string[];
  intercomId: string | null;
  processedAt: Date;
  messageIds: string[];
}

interface PatientWithMessageCount {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  conversationId: string;
  messageCount: number;
}

interface ConversationBatch {
  messages: MessageData[];
  triggerTime: Date;
  reason: string;
}

interface ModelAnalysisResult {
  model: string;
  analysis: any;
  processingTime: number;
  error?: string;
}

interface BatchResult {
  batchNumber: number;
  batch: ConversationBatch;
  modelResults: ModelAnalysisResult[];
  routerCreated?: SimulatedRouter;
}

interface HtmlReportData {
  patient: PatientWithMessageCount;
  simulationDate: Date;
  models: string[];
  isInteractive: boolean;
  totalMessages: number;
  totalBatches: number;
  batchResults: BatchResult[];
  simulatedRouters: SimulatedRouter[];
  allMessages: MessageData[];
}

@Injectable()
@Command({
  name: 'test-conversation-simulation',
  description:
    'Simulate routing behavior for an entire patient-doctor conversation',
})
export class TestConversationSimulationCommand extends CommandRunner {
  private readonly modelMap: Record<string, BedrockModel> = {
    haiku: BedrockModel.CLAUDE_3_5_HAIKU,
    sonnet: BedrockModel.CLAUDE_4_0_SONNET,
  };

  constructor(
    private readonly patientMessageRouterService: PatientMessageRouterService,
    private readonly prismaService: PrismaService,
  ) {
    super();
  }

  @Option({
    flags: '-m, --models <models>',
    description:
      'Comma-separated list of models to test (or "all" for all models)',
    defaultValue: 'sonnet',
  })
  parseModels(value: string): string[] {
    if (value.toLowerCase() === 'all') {
      return Object.keys(this.modelMap);
    }
    return value.split(',').map((m) => m.trim().toLowerCase());
  }

  @Option({
    flags: '-i, --interactive <value>',
    description:
      'Run interactively with pauses between batches (default: true)',
    defaultValue: 'true',
  })
  parseInteractive(value: string): boolean {
    return value === 'true';
  }

  @Option({
    flags: '-p, --patient <email>',
    description: 'Patient email to test directly (skips selection)',
  })
  parsePatient(value: string): string {
    return value;
  }

  @Option({
    flags: '--html <path>',
    description: 'Generate an HTML report in the specified folder',
  })
  parseHtml(value: string): string {
    return value;
  }

  async run(
    _passedParams: string[],
    options: {
      models: string[];
      interactive: boolean;
      patient?: string;
      html?: string;
    },
  ): Promise<void> {
    try {
      let selectedPatient: PatientWithMessageCount;

      if (options.patient) {
        // Direct patient email provided
        console.log(
          chalk.cyan(
            `\n🔍 Looking up patient with email: ${options.patient}\n`,
          ),
        );

        const patient = await this.getPatientByEmail(options.patient);
        if (!patient) {
          console.log(
            chalk.red(`❌ No patient found with email: ${options.patient}`),
          );
          console.log(
            chalk.yellow(
              'Make sure the patient has a patient-doctor conversation with messages.',
            ),
          );
          return;
        }

        selectedPatient = patient;
      } else {
        // Interactive patient selection
        console.log(
          chalk.cyan('\n🔍 Loading patients with conversation history...\n'),
        );

        // Get patients with conversation history, ordered by message count
        const patients = await this.getPatientsByMessageCount();

        if (patients.length === 0) {
          console.log(
            chalk.yellow('No patients with conversation history found.'),
          );
          return;
        }

        // Create searchable patient list
        const patientOptions = patients.map(
          (p) =>
            `${p.firstName} ${p.lastName} (${p.messageCount} messages) - ${p.email}`,
        );

        // Patient selection with search
        const selectedPatientString = await search({
          message: 'Select a patient to simulate (type to search):',
          source: async (input: string) => {
            if (!input) return patientOptions;
            const filtered = patientOptions.filter((option) =>
              option.toLowerCase().includes(input.toLowerCase()),
            );
            return filtered.slice(0, 20); // Limit search results to 20
          },
        });

        // Parse selection to get patient
        const selectedIndex = patientOptions.indexOf(
          selectedPatientString as string,
        );
        selectedPatient = patients[selectedIndex];
      }

      console.log(
        chalk.green(
          `\n✅ Selected: ${selectedPatient.firstName} ${selectedPatient.lastName}`,
        ),
      );
      console.log(chalk.gray(`Patient ID: ${selectedPatient.id}`));
      console.log(
        chalk.gray(`Conversation ID: ${selectedPatient.conversationId}`),
      );

      // Load full conversation
      const conversation = await this.loadConversation(
        selectedPatient.conversationId,
      );

      if (conversation.messages.length === 0) {
        console.log(chalk.yellow('No messages found in conversation.'));
        return;
      }

      console.log(
        chalk.cyan(
          `\n📊 Loaded ${conversation.messages.length} messages from conversation`,
        ),
      );

      // Detect routing batches
      const batches = this.detectRoutingBatches(conversation.messages);
      console.log(
        chalk.cyan(`\n🔄 Detected ${batches.length} routing batches\n`),
      );

      // Validate models - ensure options.models is always an array
      const modelsArray = Array.isArray(options.models)
        ? options.models
        : this.parseModels(options.models as unknown as string);
      const validModels = modelsArray.filter((m) => this.modelMap[m]);
      if (validModels.length === 0) {
        console.log(chalk.red('No valid models specified.'));
        return;
      }

      console.log(
        chalk.cyan(`🤖 Testing with models: ${validModels.join(', ')}`),
      );
      console.log(
        chalk.cyan(
          `🎮 Mode: ${options.interactive ? 'Interactive (with pauses)' : 'Non-interactive (continuous)'}\n`,
        ),
      );

      // Process each batch
      const simulatedRouters: SimulatedRouter[] = [];
      const batchResults: BatchResult[] = [];

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(
          chalk.blue(
            `\n━━━━━ Processing Batch ${i + 1}/${batches.length} ━━━━━`,
          ),
        );
        console.log(
          chalk.gray(
            `Messages: ${batch.messages.length} | Trigger: ${batch.reason}`,
          ),
        );

        // Create history up to this point
        const historyUpToBatch = this.createHistoryUpToBatch(
          conversation.messages,
          batch,
          simulatedRouters,
        );

        const activeIntercomId = historyUpToBatch.lastIntercomId;

        // Get message IDs for this batch
        const messageIds = batch.messages.map((m) => m.id);

        // Analyze with each model
        const modelResults: ModelAnalysisResult[] = [];

        for (const modelName of validModels) {
          const model = this.modelMap[modelName];
          console.log(chalk.gray(`\n  Analyzing with ${modelName}...`));

          const startTime = Date.now();
          try {
            const analysis =
              await this.patientMessageRouterService.analyzePatientMessages(
                historyUpToBatch,
                messageIds,
                model,
              );

            modelResults.push({
              model: modelName,
              analysis,
              processingTime: Date.now() - startTime,
            });
          } catch (error) {
            console.log(
              chalk.red(`  ❌ Error with ${modelName}: ${error.message}`),
            );
            modelResults.push({
              model: modelName,
              analysis: null,
              processingTime: Date.now() - startTime,
              error: error.message,
            });
          }
        }

        // Display results comparison
        this.displayBatchResults(
          i + 1,
          batch,
          modelResults,
          conversation.messages,
        );

        // Use the first successful result to update routing state
        const successfulResult = modelResults.find(
          (r) => r.analysis && !r.error,
        );
        if (successfulResult) {
          const analysis = successfulResult.analysis;

          // Check if we should create/append to Intercom
          let newIntercomId: string | null = null;
          if (analysis.relevantForPatientServices) {
            if (activeIntercomId && analysis.continuationConfidence >= 0.7) {
              console.log(
                chalk.green(
                  `\n  📮 Would append to existing Intercom conversation`,
                ),
              );
              newIntercomId = activeIntercomId;
            } else {
              newIntercomId = `sim-intercom-${uuidv4().slice(0, 8)}`;
              console.log(
                chalk.green(
                  `\n  📮 Would create new Intercom conversation: ${newIntercomId}`,
                ),
              );
            }
          }

          // Create simulated router
          const router: SimulatedRouter = {
            id: uuidv4(),
            messages: messageIds,
            status: 'processed',
            relevantForDoctor: analysis.relevantForDoctor,
            relevantForPatientServices: analysis.relevantForPatientServices,
            reason: analysis.reason || '',
            inquiryTypes: analysis.inquiryTypes || [],
            intercomId: newIntercomId,
            processedAt: batch.triggerTime,
            continuationConfidence: analysis.continuationConfidence,
            analysisSummary: analysis.messageSummary,
          };

          simulatedRouters.push(router);
        }

        // Store batch result
        batchResults.push({
          batchNumber: i + 1,
          batch,
          modelResults,
          routerCreated: successfulResult
            ? simulatedRouters[simulatedRouters.length - 1]
            : undefined,
        });

        // Pause after each batch (except the last one) if running interactively
        if (options.interactive && i < batches.length - 1) {
          console.log(
            chalk.gray('\n─────────────────────────────────────────'),
          );
          await input({
            message: chalk.cyan('Press Enter to continue to the next batch...'),
            default: '',
          });
        }
      }

      // Enhanced Summary
      this.displayFinalSummary(
        selectedPatient,
        conversation.messages,
        batches,
        simulatedRouters,
      );

      // Generate HTML report if requested
      if (options.html) {
        const reportData: HtmlReportData = {
          patient: selectedPatient,
          simulationDate: new Date(),
          models: validModels,
          isInteractive: options.interactive,
          totalMessages: conversation.messages.length,
          totalBatches: batches.length,
          batchResults,
          simulatedRouters,
          allMessages: conversation.messages,
        };

        await this.generateHtmlReport(reportData, options.html);
      }
    } catch (error) {
      console.error(chalk.red('Error:'), error.message);
      if (error.stack) {
        console.error(chalk.gray(error.stack));
      }
    }
  }

  private async getPatientsByMessageCount(): Promise<
    PatientWithMessageCount[]
  > {
    return this.prismaService.$queryRaw<PatientWithMessageCount[]>`
      SELECT 
        p.id,
        p."userId",
        u."firstName",
        u."lastName",
        u.email,
        c.id as "conversationId",
        COUNT(cm.id)::int as "messageCount"
      FROM "Patient" p
      INNER JOIN "User" u ON u.id = p."userId"
      INNER JOIN "Conversation" c ON c."patientId" = p.id
      INNER JOIN "ConversationMessage" cm ON cm."conversationId" = c.id
      WHERE c.type = 'patientDoctor'
      GROUP BY p.id, p."userId", u."firstName", u."lastName", u.email, c.id
      HAVING COUNT(cm.id) BETWEEN 1 AND 50
      ORDER BY COUNT(cm.id) DESC
      LIMIT 200
    `;
  }

  private async getPatientByEmail(
    email: string,
  ): Promise<PatientWithMessageCount | null> {
    const results = await this.prismaService.$queryRaw<
      PatientWithMessageCount[]
    >`
      SELECT 
        p.id,
        p."userId",
        u."firstName",
        u."lastName",
        u.email,
        c.id as "conversationId",
        COUNT(cm.id)::int as "messageCount"
      FROM "Patient" p
      INNER JOIN "User" u ON u.id = p."userId"
      INNER JOIN "Conversation" c ON c."patientId" = p.id
      INNER JOIN "ConversationMessage" cm ON cm."conversationId" = c.id
      WHERE c.type = 'patientDoctor' AND LOWER(u.email) = LOWER(${email})
      GROUP BY p.id, p."userId", u."firstName", u."lastName", u.email, c.id
      HAVING COUNT(cm.id) > 0
      ORDER BY COUNT(cm.id) DESC
      LIMIT 1
    `;

    return results.length > 0 ? results[0] : null;
  }

  private async loadConversation(
    conversationId: string,
  ): Promise<{ messages: MessageData[] }> {
    const messages = await this.prismaService.conversationMessage.findMany({
      where: { conversationId },
      orderBy: { createdAt: 'asc' },
      include: {
        user: true,
      },
    });

    const formattedMessages: MessageData[] = messages.map((message) => ({
      user: message.user?.type || 'unknown',
      message: message.content,
      date: message.createdAt.toISOString(),
      id: message.id,
    }));

    return { messages: formattedMessages };
  }

  private detectRoutingBatches(messages: MessageData[]): ConversationBatch[] {
    const batches: ConversationBatch[] = [];
    let currentBatch: MessageData[] = [];
    let lastPatientMessageTime: Date | null = null;

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const messageTime = new Date(message.date);

      // Only patient messages trigger routing
      if (message.user === 'patient') {
        // If we have a previous batch and this message is more than 5 minutes after the last patient message
        if (
          lastPatientMessageTime &&
          messageTime.getTime() - lastPatientMessageTime.getTime() >
            5 * 60 * 1000
        ) {
          // Close the previous batch
          if (currentBatch.length > 0) {
            batches.push({
              messages: [...currentBatch],
              triggerTime: new Date(
                lastPatientMessageTime.getTime() + 5 * 60 * 1000,
              ),
              reason: 'Routing delay reached',
            });
            currentBatch = [];
          }
        }

        // Add to current batch
        currentBatch.push(message);
        lastPatientMessageTime = messageTime;
      }
    }

    // Close any remaining batch
    if (currentBatch.length > 0 && lastPatientMessageTime) {
      batches.push({
        messages: [...currentBatch],
        triggerTime: new Date(lastPatientMessageTime.getTime() + 5 * 60 * 1000),
        reason: 'End of conversation',
      });
    }

    return batches;
  }

  private createHistoryUpToBatch(
    allMessages: MessageData[],
    currentBatch: ConversationBatch,
    simulatedRouters: SimulatedRouter[],
  ): ConversationHistoryWithRouting {
    // Calculate the 30-day threshold
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Find index of first message in current batch
    const batchStartIndex = allMessages.findIndex(
      (m) => m.id === currentBatch.messages[0].id,
    );

    // Get all messages up to and including current batch
    const messagesUpToBatch = allMessages.slice(
      0,
      batchStartIndex + currentBatch.messages.length,
    );

    // Add routing metadata to messages based on simulated routers
    const messagesWithRouting: MessageData[] = messagesUpToBatch.map((msg) => {
      const router = simulatedRouters.find((r) => r.messages.includes(msg.id));

      if (router) {
        return {
          ...msg,
          previousRouting: {
            conversationRouterId: router.id,
            status: router.status,
            relevantForDoctor: router.relevantForDoctor,
            relevantForPatientServices: router.relevantForPatientServices,
            reason: router.reason,
            inquiryTypes: router.inquiryTypes,
            intercomId: router.intercomId,
            processedAt: router.processedAt.toISOString(),
          },
        };
      }

      return msg;
    });

    // Separate messages into old and recent based on 30-day threshold
    const oldMessages = messagesWithRouting.filter(
      (message) => new Date(message.date) < thirtyDaysAgo,
    );
    const recentMessages = messagesWithRouting.filter(
      (message) => new Date(message.date) >= thirtyDaysAgo,
    );

    // Create previous routings array
    const previousRoutings: PreviousRouting[] = simulatedRouters.map(
      (router) => ({
        id: router.id,
        status: router.status,
        relevantForDoctor: router.relevantForDoctor,
        relevantForPatientServices: router.relevantForPatientServices,
        reason: router.reason,
        inquiryTypes: router.inquiryTypes,
        intercomId: router.intercomId,
        processedAt: router.processedAt,
        messageIds: router.messages,
      }),
    );

    // Separate routings into old and recent based on 30-day threshold
    const oldRoutings = previousRoutings.filter(
      (routing) => routing.processedAt < thirtyDaysAgo,
    );
    const recentRoutings = previousRoutings.filter(
      (routing) => routing.processedAt >= thirtyDaysAgo,
    );

    const activeIntercomId = previousRoutings[previousRoutings.length - 1]
      ? previousRoutings[previousRoutings.length - 1].intercomId
      : null;

    return {
      old: {
        messages: oldMessages,
        previousRoutings: oldRoutings,
      },
      recent: {
        messages: recentMessages,
        previousRoutings: recentRoutings,
      },
      hasActiveIntercomConversation: !!activeIntercomId,
      lastIntercomId: activeIntercomId,
    };
  }

  private displayBatchResults(
    batchNumber: number,
    batch: ConversationBatch,
    results: ModelAnalysisResult[],
    allMessages: MessageData[],
  ): void {
    // Create comparison table
    const headers = ['Model', 'Doctor', 'PS', 'Time (ms)', 'Summary'];

    const rows = results.map((result) => {
      if (result.error) {
        return [
          result.model,
          '❌',
          '❌',
          result.processingTime.toString(),
          chalk.red(`Error: ${result.error}`),
        ];
      }

      const analysis = result.analysis;
      return [
        result.model,
        analysis.relevantForDoctor ? '✅' : '❌',
        analysis.relevantForPatientServices ? '✅' : '❌',
        result.processingTime.toString(),
        analysis.messageSummary || '',
      ];
    });

    const tableConfig = {
      header: {
        alignment: 'center' as const,
        content: chalk.bold(`Batch ${batchNumber} Results`),
      },
      columns: {
        0: { width: 10 }, // Model
        1: { width: 8, alignment: 'center' as const }, // Doctor
        2: { width: 8, alignment: 'center' as const }, // PS
        3: { width: 10 }, // Time
        4: {
          width: 60,
          wrapWord: true,
          alignment: 'left' as const,
        }, // Summary - multi-line with word wrap
      },
    };

    console.log('\n' + table([headers, ...rows], tableConfig));

    // Show inquiry types if any
    const successfulResults = results.filter((r) => r.analysis && !r.error);
    if (successfulResults.length > 0) {
      const inquiryTypes = new Set<string>();
      successfulResults.forEach((r) => {
        (r.analysis.inquiryTypes || []).forEach((type: string) =>
          inquiryTypes.add(type),
        );
      });

      if (inquiryTypes.size > 0) {
        console.log(
          chalk.cyan('  Inquiry Types: ') + Array.from(inquiryTypes).join(', '),
        );
      }
    }

    // Display message content from the batch
    console.log(chalk.blue.bold('\n📝 Batch Messages:'));

    // Find and display the previous message if it exists
    if (batch.messages.length > 0) {
      const firstBatchMessageId = batch.messages[0].id;
      const firstBatchMessageIndex = allMessages.findIndex(
        (m) => m.id === firstBatchMessageId,
      );

      if (firstBatchMessageIndex > 0) {
        const previousMessage = allMessages[firstBatchMessageIndex - 1];
        const messageNumber = chalk.gray(`0.`);
        const date = new Date(previousMessage.date);
        const dateStr = `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        const userType = chalk.yellow(
          `[PREV MSG - ${previousMessage.user.toUpperCase()} ${dateStr}]`,
        );

        // Truncate long messages for readability
        const maxLength = 250;
        let content = previousMessage.message;
        let truncated = false;

        if (content.length > maxLength) {
          content = content.slice(0, maxLength);
          truncated = true;
        }

        // Replace newlines with spaces for cleaner display
        content = content.replace(/\n+/g, ' ').trim();
        const truncationIndicator = truncated ? chalk.gray('...') : '';

        console.log(
          `   ${messageNumber} ${userType} ${content}${truncationIndicator}`,
        );
      }
    }

    // Display the actual batch messages
    batch.messages.forEach((message: MessageData, index: number) => {
      const messageNumber = chalk.gray(`${index + 1}.`);
      const date = new Date(message.date);
      const dateStr = `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      const userType = chalk.cyan(`[${message.user.toUpperCase()} ${dateStr}]`);

      // Truncate long messages for readability
      const maxLength = 250;
      let content = message.message;
      let truncated = false;

      if (content.length > maxLength) {
        content = content.slice(0, maxLength);
        truncated = true;
      }

      // Replace newlines with spaces for cleaner display
      content = content.replace(/\n+/g, ' ').trim();

      const truncationIndicator = truncated ? chalk.gray('...') : '';

      console.log(
        `   ${messageNumber} ${userType} ${content}${truncationIndicator}`,
      );
    });
  }

  private displayFinalSummary(
    patient: PatientWithMessageCount,
    messages: MessageData[],
    batches: ConversationBatch[],
    simulatedRouters: SimulatedRouter[],
  ): void {
    console.log(chalk.cyan('\n\n' + '═'.repeat(80)));
    console.log(chalk.cyan.bold('                      📊 SIMULATION SUMMARY'));
    console.log(chalk.cyan('═'.repeat(80) + '\n'));

    // Patient Info
    console.log(chalk.blue.bold('👤 Patient Information:'));
    console.log(`   Name: ${patient.firstName} ${patient.lastName}`);
    console.log(`   Email: ${patient.email}`);
    console.log(`   Total Messages: ${messages.length}`);
    console.log(
      `   Conversation Duration: ${this.formatDuration(
        new Date(messages[0].date),
        new Date(messages[messages.length - 1].date),
      )}`,
    );
    console.log();

    // Routing Statistics
    console.log(chalk.blue.bold('🔄 Routing Statistics:'));
    const routingTable = [
      ['Metric', 'Count', 'Percentage'],
      ['Total Routing Batches', batches.length.toString(), '100%'],
      [
        'Doctor-Relevant',
        simulatedRouters.filter((r) => r.relevantForDoctor).length.toString(),
        `${Math.round(
          (simulatedRouters.filter((r) => r.relevantForDoctor).length /
            batches.length) *
            100,
        )}%`,
      ],
      [
        'PS-Relevant',
        simulatedRouters
          .filter((r) => r.relevantForPatientServices)
          .length.toString(),
        `${Math.round(
          (simulatedRouters.filter((r) => r.relevantForPatientServices).length /
            batches.length) *
            100,
        )}%`,
      ],
      [
        'Both Doctor & PS',
        simulatedRouters
          .filter((r) => r.relevantForDoctor && r.relevantForPatientServices)
          .length.toString(),
        `${Math.round(
          (simulatedRouters.filter(
            (r) => r.relevantForDoctor && r.relevantForPatientServices,
          ).length /
            batches.length) *
            100,
        )}%`,
      ],
      [
        'Neither (No Action)',
        simulatedRouters
          .filter((r) => !r.relevantForDoctor && !r.relevantForPatientServices)
          .length.toString(),
        `${Math.round(
          (simulatedRouters.filter(
            (r) => !r.relevantForDoctor && !r.relevantForPatientServices,
          ).length /
            batches.length) *
            100,
        )}%`,
      ],
    ];
    console.log(table(routingTable));

    // Intercom Analysis
    console.log(chalk.blue.bold('📮 Intercom Conversation Analysis:'));
    const intercomConversations =
      this.analyzeIntercomConversations(simulatedRouters);
    const intercomTable = [
      ['Conversation ID', 'Messages', 'Duration'],
      ...intercomConversations.map((conv) => [
        conv.id.slice(0, 20) + '...',
        conv.messageCount.toString(),
        this.formatDuration(conv.startTime, conv.endTime),
      ]),
    ];
    if (intercomConversations.length > 0) {
      console.log(table(intercomTable));
    } else {
      console.log(chalk.gray('   No Intercom conversations would be created'));
    }
    console.log();

    // Inquiry Types Summary
    console.log(chalk.blue.bold('🏷️  Inquiry Types Distribution:'));
    const inquiryTypeCounts = this.countInquiryTypes(simulatedRouters);
    if (inquiryTypeCounts.size > 0) {
      const inquiryTable = [
        ['Inquiry Type', 'Count', 'Percentage'],
        ...Array.from(inquiryTypeCounts.entries())
          .sort((a, b) => b[1] - a[1])
          .map(([type, count]) => [
            type,
            count.toString(),
            `${Math.round((count / simulatedRouters.length) * 100)}%`,
          ]),
      ];
      console.log(table(inquiryTable));
    } else {
      console.log(chalk.gray('   No inquiry types detected'));
    }

    // Timeline Visualization
    console.log(chalk.blue.bold('📅 Conversation Timeline:'));
    this.displayTimeline(messages, simulatedRouters);

    // Recommendations
    console.log(chalk.blue.bold('\n💡 Insights & Recommendations:'));
    const insights = this.generateInsights(messages, batches, simulatedRouters);
    insights.forEach((insight) => {
      console.log(`   • ${insight}`);
    });

    console.log(chalk.cyan('\n' + '═'.repeat(80) + '\n'));
  }

  private analyzeIntercomConversations(routers: SimulatedRouter[]): Array<{
    id: string;
    messageCount: number;
    startTime: Date;
    endTime: Date;
    avgConfidence: number;
  }> {
    const conversations = new Map<
      string,
      {
        routers: SimulatedRouter[];
        messageCount: number;
        confidences: number[];
      }
    >();

    routers
      .filter((r) => r.intercomId)
      .forEach((router) => {
        const existing = conversations.get(router.intercomId!) || {
          routers: [],
          messageCount: 0,
          confidences: [],
        };
        existing.routers.push(router);
        existing.messageCount += router.messages.length;
        if (router.continuationConfidence !== undefined) {
          existing.confidences.push(router.continuationConfidence);
        }
        conversations.set(router.intercomId!, existing);
      });

    return Array.from(conversations.entries()).map(([id, data]) => ({
      id,
      messageCount: data.messageCount,
      startTime: data.routers[0].processedAt,
      endTime: data.routers[data.routers.length - 1].processedAt,
      avgConfidence:
        data.confidences.reduce((a, b) => a + b, 0) / data.confidences.length ||
        0,
    }));
  }

  private countInquiryTypes(routers: SimulatedRouter[]): Map<string, number> {
    const counts = new Map<string, number>();
    routers.forEach((router) => {
      router.inquiryTypes.forEach((type) => {
        counts.set(type, (counts.get(type) || 0) + 1);
      });
    });
    return counts;
  }

  private formatDuration(start: Date, end: Date): string {
    const diff = end.getTime() - start.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    const parts = [];
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);

    return parts.join(' ') || '< 1m';
  }

  private displayTimeline(
    messages: MessageData[],
    routers: SimulatedRouter[],
  ): void {
    // Create a simplified timeline showing key events
    const events: Array<{ time: Date; event: string; type: string }> = [];

    // Add first and last message
    events.push({
      time: new Date(messages[0].date),
      event: 'Conversation started',
      type: 'start',
    });

    // Add routing events
    routers.forEach((router, index) => {
      if (router.relevantForDoctor || router.relevantForPatientServices) {
        const targets = [];
        if (router.relevantForDoctor) targets.push('Doctor');
        if (router.relevantForPatientServices) targets.push('PS');

        events.push({
          time: router.processedAt,
          event: `Batch ${index + 1} routed to ${targets.join(' & ')}`,
          type: 'routing',
        });
      }

      if (
        router.intercomId &&
        !routers.slice(0, index).some((r) => r.intercomId === router.intercomId)
      ) {
        events.push({
          time: router.processedAt,
          event: 'New Intercom conversation created',
          type: 'intercom',
        });
      }
    });

    // Add conversation ended event after all routing events
    // Find the latest routing event time, or fall back to last message time
    const lastRoutingTime =
      routers.length > 0
        ? Math.max(...routers.map((r) => r.processedAt.getTime()))
        : new Date(messages[messages.length - 1].date).getTime();

    events.push({
      time: new Date(lastRoutingTime),
      event: 'Conversation ended',
      type: 'end',
    });

    // Sort by time
    events.sort((a, b) => a.time.getTime() - b.time.getTime());

    // Display timeline
    console.log();
    events.forEach((event, index) => {
      const icon =
        event.type === 'start'
          ? '🟢'
          : event.type === 'end'
            ? '🔴'
            : event.type === 'routing'
              ? '🔄'
              : event.type === 'intercom'
                ? '📮'
                : '⚫';

      const timeStr = event.time.toLocaleString();
      console.log(`   ${icon} ${chalk.gray(timeStr)} - ${event.event}`);

      if (index < events.length - 1) {
        const nextEvent = events[index + 1];
        const duration = this.formatDuration(event.time, nextEvent.time);
        console.log(chalk.gray(`   │  ↓ ${duration}`));
      }
    });
  }

  private generateInsights(
    messages: MessageData[],
    batches: ConversationBatch[],
    routers: SimulatedRouter[],
  ): string[] {
    const insights: string[] = [];

    // Response time analysis
    const patientMessages = messages.filter((m) => m.user === 'patient');
    const doctorMessages = messages.filter((m) => m.user === 'doctor');

    if (doctorMessages.length > 0 && patientMessages.length > 0) {
      insights.push(
        `Patient sent ${patientMessages.length} messages, doctor sent ${doctorMessages.length} messages`,
      );
    }

    // Routing efficiency
    const routedBatches = routers.filter(
      (r) => r.relevantForDoctor || r.relevantForPatientServices,
    );
    const routingEfficiency = (routedBatches.length / batches.length) * 100;

    if (routingEfficiency < 50) {
      insights.push(
        `Low routing rate (${routingEfficiency.toFixed(0)}%) - many messages didn't require action`,
      );
    } else if (routingEfficiency > 80) {
      insights.push(
        `High routing rate (${routingEfficiency.toFixed(0)}%) - conversation required frequent attention`,
      );
    }

    // Common inquiry types
    const inquiryTypes = this.countInquiryTypes(routers);
    if (inquiryTypes.size > 0) {
      const topType = Array.from(inquiryTypes.entries()).sort(
        (a, b) => b[1] - a[1],
      )[0];
      insights.push(
        `Most common inquiry type: "${topType[0]}" (${topType[1]} occurrences)`,
      );
    }

    // Conversation duration
    const duration =
      new Date(messages[messages.length - 1].date).getTime() -
      new Date(messages[0].date).getTime();
    const durationDays = duration / (1000 * 60 * 60 * 24);

    if (durationDays < 1) {
      insights.push('Quick conversation resolved within a day');
    } else if (durationDays > 30) {
      insights.push(
        `Long-running conversation spanning ${Math.round(durationDays)} days`,
      );
    }

    return insights;
  }

  private async generateHtmlReport(
    data: HtmlReportData,
    outputPath: string,
  ): Promise<void> {
    try {
      // Ensure the output directory exists
      const resolvedPath = path.resolve(outputPath);
      if (!fs.existsSync(resolvedPath)) {
        fs.mkdirSync(resolvedPath, { recursive: true });
      }

      // Generate filename with timestamp
      const timestamp = data.simulationDate.toISOString().replace(/[:.]/g, '-');
      const filename = `conversation-simulation-${data.patient.firstName}-${data.patient.lastName}-${timestamp}.html`;
      const filePath = path.join(resolvedPath, filename);

      // Generate HTML content
      const html = this.generateHtmlContent(data);

      // Write file
      fs.writeFileSync(filePath, html, 'utf-8');

      console.log(chalk.green(`\n✅ HTML report generated: ${filePath}`));
    } catch (error) {
      console.error(
        chalk.red(`\n❌ Failed to generate HTML report: ${error.message}`),
      );
    }
  }

  private generateHtmlContent(data: HtmlReportData): string {
    const {
      patient,
      simulationDate,
      models,
      isInteractive,
      totalMessages,
      totalBatches,
      batchResults,
      simulatedRouters,
      allMessages,
    } = data;

    // Calculate statistics
    const routingStats = this.calculateRoutingStats(
      simulatedRouters,
      totalBatches,
    );
    const intercomConversations =
      this.analyzeIntercomConversations(simulatedRouters);
    const inquiryTypeCounts = this.countInquiryTypes(simulatedRouters);

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation Simulation Report - ${patient.email}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
        }
        .info-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .info-card p {
            margin: 0;
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
        }
        .batch {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .batch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .batch-number {
            font-size: 1.2em;
            font-weight: bold;
            color: #3498db;
        }
        .batch-info {
            color: #6c757d;
            font-size: 0.9em;
        }
        .message {
            background-color: white;
            border-left: 3px solid #3498db;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 0 4px 4px 0;
        }
        .message.patient {
            border-left-color: #2ecc71;
        }
        .message.doctor {
            border-left-color: #e74c3c;
        }
        .message.prev {
            border-left-color: #f39c12;
            opacity: 0.7;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.85em;
            color: #6c757d;
        }
        .message-user {
            font-weight: bold;
            text-transform: uppercase;
        }
        .message-content {
            color: #333;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: 500;
            margin-right: 5px;
        }
        .badge-doctor {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .badge-ps {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        .badge-intercom {
            background-color: #e8f5e9;
            color: #388e3c;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
            margin: 20px 0;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -24px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #3498db;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #dee2e6;
        }
        .timeline-content {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .summary-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }
        .summary-card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #3498db;
            transition: width 0.3s ease;
            text-align: center;
            color: white;
            font-size: 0.85em;
            line-height: 20px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Conversation Simulation Report</h1>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>Patient</h3>
                <p>${patient.firstName} ${patient.lastName}</p>
                <small>${patient.email}</small>
            </div>
            <div class="info-card">
                <h3>Simulation Date</h3>
                <p>${simulationDate.toLocaleString()}</p>
            </div>
            <div class="info-card">
                <h3>Models Tested</h3>
                <p>${models.join(', ')}</p>
            </div>
            <div class="info-card">
                <h3>Mode</h3>
                <p>${isInteractive ? 'Interactive' : 'Non-interactive'}</p>
            </div>
            <div class="info-card">
                <h3>Total Messages</h3>
                <p>${totalMessages}</p>
            </div>
            <div class="info-card">
                <h3>Routing Batches</h3>
                <p>${totalBatches}</p>
            </div>
        </div>

        <h2>🔄 Routing Statistics</h2>
        <table>
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Count</th>
                    <th>Percentage</th>
                    <th>Visual</th>
                </tr>
            </thead>
            <tbody>
                ${Object.entries(routingStats)
                  .map(
                    ([key, value]) => `
                <tr>
                    <td>${key}</td>
                    <td>${value.count}</td>
                    <td>${value.percentage}%</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${value.percentage}%">${value.percentage}%</div>
                        </div>
                    </td>
                </tr>
                `,
                  )
                  .join('')}
            </tbody>
        </table>

        <h2>📝 Batch Analysis</h2>
        ${batchResults.map((result) => this.generateBatchHtml(result, allMessages, simulatedRouters)).join('')}

        ${
          intercomConversations.length > 0
            ? `
        <h2>📮 Intercom Conversations</h2>
        <table>
            <thead>
                <tr>
                    <th>Conversation ID</th>
                    <th>Messages</th>
                    <th>Duration</th>
                    <th>Avg Confidence</th>
                </tr>
            </thead>
            <tbody>
                ${intercomConversations
                  .map(
                    (conv) => `
                <tr>
                    <td><code>${conv.id}</code></td>
                    <td>${conv.messageCount}</td>
                    <td>${this.formatDuration(conv.startTime, conv.endTime)}</td>
                    <td>${conv.avgConfidence.toFixed(2)}</td>
                </tr>
                `,
                  )
                  .join('')}
            </tbody>
        </table>
        `
            : ''
        }

        ${
          inquiryTypeCounts.size > 0
            ? `
        <h2>🏷️ Inquiry Types</h2>
        <table>
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Count</th>
                    <th>Percentage</th>
                </tr>
            </thead>
            <tbody>
                ${Array.from(inquiryTypeCounts.entries())
                  .sort((a, b) => b[1] - a[1])
                  .map(
                    ([type, count]) => `
                <tr>
                    <td>${type}</td>
                    <td>${count}</td>
                    <td>${Math.round((count / simulatedRouters.length) * 100)}%</td>
                </tr>
                `,
                  )
                  .join('')}
            </tbody>
        </table>
        `
            : ''
        }

        <h2>📅 Conversation Timeline</h2>
        <div class="timeline">
            ${this.generateTimelineHtml(allMessages, simulatedRouters)}
        </div>

        <h2>💡 Insights & Recommendations</h2>
        <ul>
            ${this.generateInsights(
              allMessages,
              batchResults.map((r) => r.batch),
              simulatedRouters,
            )
              .map((insight) => `<li>${insight}</li>`)
              .join('')}
        </ul>

        <div class="footer">
            Generated by Willow Conversation Simulation Tool • ${new Date().toLocaleString()}
        </div>
    </div>
</body>
</html>`;
  }

  private generateBatchHtml(
    result: BatchResult,
    allMessages: MessageData[],
    simulatedRouters: SimulatedRouter[],
  ): string {
    const { batchNumber, batch, modelResults } = result;

    // Find previous message if exists
    let prevMessageHtml = '';
    if (batch.messages.length > 0) {
      const firstBatchMessageId = batch.messages[0].id;
      const firstBatchMessageIndex = allMessages.findIndex(
        (m) => m.id === firstBatchMessageId,
      );

      if (firstBatchMessageIndex > 0) {
        const previousMessage = allMessages[firstBatchMessageIndex - 1];
        const date = new Date(previousMessage.date);
        const dateStr = `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        prevMessageHtml = `
        <div class="message prev ${previousMessage.user}">
            <div class="message-header">
                <span class="message-user">[PREV MSG - ${previousMessage.user.toUpperCase()}]</span>
                <span>${dateStr}</span>
            </div>
            <div class="message-content">${this.escapeHtml(previousMessage.message)}</div>
        </div>
        `;
      }
    }

    // Generate messages HTML
    const messagesHtml = batch.messages
      .map((message: MessageData, index: number) => {
        const date = new Date(message.date);
        const dateStr = `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;

        return `
      <div class="message ${message.user}">
          <div class="message-header">
              <span class="message-user">${index + 1}. ${message.user.toUpperCase()}</span>
              <span>${dateStr}</span>
          </div>
          <div class="message-content">${this.escapeHtml(message.message)}</div>
      </div>
      `;
      })
      .join('');

    // Generate model results table
    const modelResultsHtml = `
    <table>
        <thead>
            <tr>
                <th>Model</th>
                <th>Doctor</th>
                <th>PS</th>
                <th>Time (ms)</th>
                <th>Summary</th>
            </tr>
        </thead>
        <tbody>
            ${modelResults
              .map((result) => {
                if (result.error) {
                  return `
                <tr>
                    <td>${result.model}</td>
                    <td colspan="4" class="error">Error: ${result.error}</td>
                </tr>
                `;
                }
                const analysis = result.analysis;
                return `
              <tr>
                  <td>${result.model}</td>
                  <td>${analysis.relevantForDoctor ? '<span class="success">✅</span>' : '<span class="error">❌</span>'}</td>
                  <td>${analysis.relevantForPatientServices ? '<span class="success">✅</span>' : '<span class="error">❌</span>'}</td>
                  <td>${result.processingTime}</td>
                  <td style="word-wrap: break-word; max-width: 400px;">${this.escapeHtml(String(analysis.messageSummary || ''))}</td>
              </tr>
              `;
              })
              .join('')}
        </tbody>
    </table>
    `;

    // Get inquiry types
    const inquiryTypes = new Set<string>();
    modelResults.forEach((r) => {
      if (r.analysis && !r.error) {
        (r.analysis.inquiryTypes || []).forEach((type: string) =>
          inquiryTypes.add(type),
        );
      }
    });

    return `
    <div class="batch">
        <div class="batch-header">
            <span class="batch-number">Batch ${batchNumber}</span>
            <span class="batch-info">${batch.messages.length} messages | Trigger: ${batch.reason}</span>
        </div>
        
        ${prevMessageHtml}
        ${messagesHtml}
        
        <h3>Model Analysis Results</h3>
        ${modelResultsHtml}
        
        ${
          inquiryTypes.size > 0
            ? `
        <div style="margin-top: 15px;">
            <strong>Inquiry Types:</strong>
            ${Array.from(inquiryTypes)
              .map((type) => `<span class="badge badge-ps">${type}</span>`)
              .join('')}
        </div>
        `
            : ''
        }
        
        ${
          result.routerCreated
            ? `
        <div style="margin-top: 15px;">
            ${result.routerCreated.relevantForDoctor ? '<span class="badge badge-doctor">Sent to Doctor</span>' : ''}
            ${result.routerCreated.relevantForPatientServices ? '<span class="badge badge-ps">Sent to Patient Services</span>' : ''}
            ${result.routerCreated.intercomId ? this.generateIntercomBadge(result.routerCreated, simulatedRouters) : ''}
        </div>
        `
            : ''
        }
    </div>
    `;
  }

  private calculateRoutingStats(
    routers: SimulatedRouter[],
    totalBatches: number,
  ): Record<string, { count: number; percentage: number }> {
    const stats = {
      'Total Routing Batches': {
        count: totalBatches,
        percentage: 100,
      },
      'Doctor-Relevant': {
        count: routers.filter((r) => r.relevantForDoctor).length,
        percentage: Math.round(
          (routers.filter((r) => r.relevantForDoctor).length / totalBatches) *
            100,
        ),
      },
      'PS-Relevant': {
        count: routers.filter((r) => r.relevantForPatientServices).length,
        percentage: Math.round(
          (routers.filter((r) => r.relevantForPatientServices).length /
            totalBatches) *
            100,
        ),
      },
      'Both Doctor & PS': {
        count: routers.filter(
          (r) => r.relevantForDoctor && r.relevantForPatientServices,
        ).length,
        percentage: Math.round(
          (routers.filter(
            (r) => r.relevantForDoctor && r.relevantForPatientServices,
          ).length /
            totalBatches) *
            100,
        ),
      },
      'Neither (No Action)': {
        count: routers.filter(
          (r) => !r.relevantForDoctor && !r.relevantForPatientServices,
        ).length,
        percentage: Math.round(
          (routers.filter(
            (r) => !r.relevantForDoctor && !r.relevantForPatientServices,
          ).length /
            totalBatches) *
            100,
        ),
      },
    };

    return stats;
  }

  private generateTimelineHtml(
    messages: MessageData[],
    routers: SimulatedRouter[],
  ): string {
    const events: Array<{ time: Date; event: string; type: string }> = [];

    // Add first and last message
    events.push({
      time: new Date(messages[0].date),
      event: 'Conversation started',
      type: 'start',
    });

    // Add routing events
    routers.forEach((router, index) => {
      if (router.relevantForDoctor || router.relevantForPatientServices) {
        const targets = [];
        if (router.relevantForDoctor) targets.push('Doctor');
        if (router.relevantForPatientServices) targets.push('PS');

        events.push({
          time: router.processedAt,
          event: `Batch ${index + 1} routed to ${targets.join(' & ')}`,
          type: 'routing',
        });
      }

      if (
        router.intercomId &&
        !routers.slice(0, index).some((r) => r.intercomId === router.intercomId)
      ) {
        events.push({
          time: router.processedAt,
          event: 'New Intercom conversation created',
          type: 'intercom',
        });
      }
    });

    // Add conversation ended event after all routing events
    // Find the latest routing event time, or fall back to last message time
    const lastRoutingTime =
      routers.length > 0
        ? Math.max(...routers.map((r) => r.processedAt.getTime()))
        : new Date(messages[messages.length - 1].date).getTime();

    events.push({
      time: new Date(lastRoutingTime),
      event: 'Conversation ended',
      type: 'end',
    });

    // Sort by time
    events.sort((a, b) => a.time.getTime() - b.time.getTime());

    return events
      .map((event, index) => {
        const icon =
          event.type === 'start'
            ? '🟢'
            : event.type === 'end'
              ? '🔴'
              : event.type === 'routing'
                ? '🔄'
                : event.type === 'intercom'
                  ? '📮'
                  : '⚫';

        const timeStr = event.time.toLocaleString();
        const duration =
          index < events.length - 1
            ? this.formatDuration(event.time, events[index + 1].time)
            : null;

        return `
      <div class="timeline-item">
          <div class="timeline-content">
              <div>${icon} <strong>${event.event}</strong></div>
              <div style="color: #6c757d; font-size: 0.9em;">${timeStr}</div>
              ${duration ? `<div style="color: #6c757d; font-size: 0.85em; margin-top: 5px;">↓ ${duration}</div>` : ''}
          </div>
      </div>
      `;
      })
      .join('');
  }

  private generateIntercomBadge(
    currentRouter: SimulatedRouter,
    allRouters: SimulatedRouter[],
  ): string {
    if (!currentRouter.intercomId) {
      return '';
    }

    // Find the index of the current router
    const currentIndex = allRouters.findIndex((r) => r.id === currentRouter.id);

    // Check if any previous router used the same intercom ID
    const previousRoutersWithSameIntercomId = allRouters
      .slice(0, currentIndex)
      .filter((r) => r.intercomId === currentRouter.intercomId);

    if (previousRoutersWithSameIntercomId.length > 0) {
      // This is continuing an existing conversation
      return '<span class="badge badge-intercom">Updated previous intercom chat</span>';
    } else {
      // This is a new conversation
      return '<span class="badge badge-intercom">Send to Intercom</span>';
    }
  }

  private escapeHtml(text: string): string {
    const map: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;',
    };
    return text.replace(/[&<>"']/g, (m) => map[m]);
  }
}
