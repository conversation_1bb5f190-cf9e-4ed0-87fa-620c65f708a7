import type {
  Conversation as ConversationDb,
  Patient as PatientDb,
} from '@willow/db';

import type { ProductPrice } from '~/data/dashboard-types';

export interface Patient {
  id: string;
  userId: string;
  stateId: string;
  questionnaireId: string;
  doctorId: string;
  stripeCustomerId: string;
  doseSpotPatientId: string;
  pharmacyId: string;
  birthDate: string;
  gender: string;
  height: number;
  weight: number;
  idPhoto: string;
  facePhoto: string;
  identityVerificationType?: 'document' | 'photoId';
  lastFourSSN?: string;
  vouchedVerifiedAt?: Date;
  onboardingState: OnboardingState;
  status: PatientDb['status'];
  statusBeforeCancellation?: PatientDb['status'];
  verificationStatus: PatientDb['verificationStatus'];
  rejectedReason: any;
  verifiedByUser: any;
  acceptedByUser: string;
  getPromotionsSMS: boolean;
  createdAt: string;
  updatedAt: string;
  acceptedAt: string;
  verifiedAt: any;
  user: User;
  desiredTreatments: DesiredTreatment[];
  shippingAddresses: ShippingAddress[];
  pharmacy: Pharmacy;
  hasTreatment: boolean;
  sso: string;
  state: State;
  conversation?: Conversation;
  canceledAt?: Date;
  canceledBy?: User;
  rejectedAt?: Date;
  rejectedStatus: string;
  doctorAdminConversation?: {
    // this should be a gllbal type
    id: string;
    status: ConversationDb['status'];
  };
}

export interface OnboardingState {
  context: Context;
}

export interface Context {
  questionnaire: Questionnaire;
}

export interface Pharmacy {
  priority: number;
  id: string;
  name: string;
  metadata: Record<string, string>;
  color?: string;
}

export interface Questionnaire {
  birthDate: string;
  gender: string;
  isPregnant: string;
  usingGLP1: string;
  haveDiabetes: string;
  doctorVisits: string;
  qualifyingConditions: string[];
  height: number;
  weight: number;
  hasAllergies: string;
  allergies: string[];
  medications: string[];
  medicalConditions: string[];
  objectives: string[];
  additionalInformation: string;
}

export interface User {
  id: string;
  type: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  createdAt: string;
  conversations: Conversation[];
}

export interface Conversation {
  id: string;
  userId: string;
  patientId: string;
  status: string;
  type: string;
  lastMessageText: string;
  lastMessageFrom: string;
  createdAt: string;
  updatedAt: string;
}

export interface DesiredTreatment {
  vials: number;
  patientId: string;
  productId: string;
  product: Product;
}

export interface Product {
  id: string;
  defaultPriceId: string;
  name: string;
  image: string;
  description: string;
  active: boolean;
  metadata: ProductMetadata;
  createdAt: string;
}

export type ProductWithVials = Product & {
  vials: number;
  productPrice: ProductPrice[];
};

export interface ProductMetadata {
  customBehavior: string;
  category: string;
  vialsize: string;
  totalvial: string;
  concentration: string;
  form: string;
  label: string;
  notice: string;
  onboardingLabel: string;
  order: string;
  pharmacy: string;
  supplyLength: string;
  tags: string;
  type: string;
  weightLossMultiplier: string;
}

export interface ShippingAddress {
  id: string;
  patientId: string;
  address1: string;
  address2: string;
  city: string;
  stateId: string;
  zip: string;
  default: boolean;
  createdAt: string;
}

export interface State {
  id: string;
  name: string;
  code: string;
  enabled: boolean;
}
