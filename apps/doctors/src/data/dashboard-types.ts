import type { ShippingAddress, State } from '@/data/patient-types';

import type { PatientFollowUp as PrismaPatientFollowUp } from '@willow/db';

import type { Message } from './chat';

export interface DashboardHomePageData {
  availablePatients: AvailablePatient[];
  pendingApprovalPatients: PendingApprovalPatient[];
  revalidatedPatients: PendingApprovalPatient[];
  lastMessages: LastMessage[];
}

export interface AvailablePatient {
  id: string;
  birthDate: string;
  gender: string;
  status: string;
  createdAt: string;
  completedAt: string;
  user: User;
  state: State;
  shippingAddresses: ShippingAddress[];
}

interface Treatment {
  id: string;
  productPrice: {
    productName: string;
    priceName: string;
  };
  treatmentId: string;
  prescriptionId: string;
  prescriptionDate: string;
  form: string;
  vials: number;
}

export interface PrescribePatient {
  updatedAt: string;
  treatments: Treatment[];
  pharmacy: {
    metadata: Record<string, any>;
    name: string;
    color?: string;
  };
  patientId: string;
  birthDate: string;
  user: {
    firstName: string;
    lastName: string;
  };
  state: {
    name: string;
    code: string;
  };
  sso: string;
}

export interface User {
  id: string;
  type: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  createdAt: string;
}

export interface PendingApprovalPatient {
  id: string;
  birthDate: string;
  gender: string;
  status: string;
  createdAt: string;
  acceptedAt: string;

  verificationStatus: string;
  user: PendingApprovalUser;
  state: State;
  historicAssignments: {
    previousAssignment: {
      doctor: {
        user: User;
      };
    };
    BulkTransfer?: {
      id: string;
      status: string;
    };
  }[];
}

export interface Prescription {
  id: string;
  doctorId: string;
  patientId: string;
  productId: string;
  productPriceId: string;
  doseSpotPrescriptionId: any;
  stripeInvoiceId: string;
  status: string;
  createdAt: string;
  updatedAt: any;
  lexiGenProductId: number;
  paymentStatus: any;
  doseSpotStatus: any;
  product: Product;
  productPrice: ProductPrice;
}

export interface Product {
  name: string;
}

export interface ProductPrice {
  id: string;
  name: string;
  unit_amount: number;
  metadata: {
    phase: number;
    dosageLabel: string;
  };
}

export interface PendingApprovalUser {
  id: string;
  type: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  createdAt: string;
  conversations: Conversation[];
}

export interface Conversation {
  id: string;
  userId: string;
  patientId: string;
  status: string;
  type: string;
  contentType: 'text' | 'image';
  lastMessageText?: string;
  lastMessageFrom?: string;
  createdAt: string;
  updatedAt: string;
  lastMessageUser?: LastMessageUser;
  patient: {
    user: {
      firstName: string;
      lastName: string;
    };
  };
  unreadMessages: Pick<
    Message,
    'contentType' | 'content' | 'id' | 'userId' | 'type'
  >[];
}

export interface LastMessage {
  id: string;
  conversationId: string;
  userId: string;
  unreadMessages: number;
  updatedAt: string;
  needsReply: boolean;
  conversation: Conversation;
}

export interface LastMessageUser {
  id: string;
  type: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: any;
  createdAt: string;
}

export interface PatientFollowUp {
  hasDueFollowUp: boolean;
  scheduledAt: string;
  patientId: string;
  status?:
    | 'scheduled'
    | 'paused'
    | 'completedByPatient'
    | 'reviewedByDoctor'
    | 'cancelled';
}

type YesNo = 'yes' | 'no';

export interface FollowUpMachineContext {
  rejected?: boolean;
  questionnaireCompleted: boolean;
  completedAt?: string;
  questionnaire: {
    startWeight?: number;
    currentWeight?: number;
    goalWeight?: number;
    areThereMedicalHistoryChanges?: YesNo;
    medicalHistoryChanges?: string;
    areThereMedicationSideEffects?: YesNo;
    medicationSideEffects?: string;
    isContinueMedication?: YesNo;
    stopMedicationReason?: string;
    isContinueCurrentDose: YesNo;
    isIncreaseSupply?: YesNo;
    changeDoseReason?: string;
    questions?: string;
    moreInfo?: string;
  };
}

export type FollowUpWithContext = Omit<
  PrismaPatientFollowUp,
  'completedAt' | 'createdAt' | 'updatedAt'
> & {
  completedAt: string | null;
  createdAt: string;
  updatedAt: string;
  context: FollowUpMachineContext;
};
