import type { FC } from 'react';
import { useState } from 'react';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { addHours, format, intlFormat, startOfDay } from 'date-fns';
import { Calendar, Clock } from 'lucide-react';

import { Button } from '@willow/ui/base/button';

import { combineDateTime } from '~/lib/utils';

interface SubscriptionStartDateProps {
  onChangeDate: (date: Date) => void;
  defaultValue?: Date;
}

const SubscriptionStartDate: FC<SubscriptionStartDateProps> = ({
  onChangeDate,
  defaultValue,
}) => {
  const [selectedDate, setSelectedDate] = useState<Date>(
    defaultValue ?? new Date(),
  );
  const [selectedTime, setSelectedTime] = useState<string>(
    format(defaultValue ?? new Date(), 'HH:mm'),
  );
  const [previousSelectedDate, setPreviousSelectedDate] =
    useState<Date>(selectedDate);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
    }
  };

  const handleDateConfirm = () => {
    const updatedTime = format(addHours(new Date(), 36), 'HH:mm');
    setPreviousSelectedDate(selectedDate);
    setSelectedTime(updatedTime);

    const dateWithTime = combineDateTime(selectedDate, updatedTime);

    handleDateSelect(dateWithTime);
    onChangeDate(dateWithTime);
    setIsDatePickerOpen(false);
  };

  return (
    <div className="relative flex flex-col gap-12 bg-gray-50 px-6 py-8">
      <div className="flex flex-col gap-1">
        <span className="pr-4 text-sm font-medium leading-none text-denim">
          Subscription Start Date
        </span>
        <div className="flex flex-row items-center gap-4">
          <Popover
            open={isDatePickerOpen}
            onOpenChange={(val) => {
              setIsDatePickerOpen(val);
              setSelectedDate(previousSelectedDate);
            }}
          >
            <PopoverTrigger asChild>
              <div className="flex h-10 w-fit cursor-pointer items-center gap-2 border-b border-denim p-2 text-sm">
                <Calendar className="text-denim" size={16} />
                <span>
                  {intlFormat(selectedDate, {
                    dateStyle: 'long',
                  })}
                </span>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={(date) => date < startOfDay(new Date())}
                initialFocus
              />
              <div className="flex justify-end gap-2 p-2">
                <Button
                  variant={'denimOutline'}
                  size="xs"
                  className="border py-4"
                  type="reset"
                  onClick={() => {
                    setSelectedDate(previousSelectedDate);
                    setIsDatePickerOpen(false);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant={'denim'}
                  className="border border-denim px-6 py-4 text-white"
                  size="xs"
                  onClick={handleDateConfirm}
                >
                  Done
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          <div className="relative flex items-center gap-2 border-b border-denim p-2 opacity-50">
            <Clock className="text-denim" size={16} />
            <div className="pr-1">{selectedTime}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionStartDate;
