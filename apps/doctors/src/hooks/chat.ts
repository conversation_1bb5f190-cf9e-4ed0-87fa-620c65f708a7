import { useMutation, useQuery } from '@tanstack/react-query';

import type { ConversationWatcher } from '@willow/db/client';
import { apiClient } from '@willow/utils/api/client';
import { useInvalidatedQuery } from '@willow/utils/react-query';

export const useConversationWatcher = (
  userId: string,
  conversationId: string,
) => {
  return useQuery({
    queryKey: ['watcher', conversationId],
    queryFn: () =>
      apiClient
        .get(`/chat/${conversationId}/watcher/${userId}`)
        .then((res) => res.data as ConversationWatcher),
  });
};

export const useToggleNeedsReply = (conversationId: string) => {
  return useMutation<unknown, unknown, { needsReply: boolean }>({
    mutationFn: (data) =>
      apiClient.post(`/chat/${conversationId}/needsReplyToggle`, data),
  });
};

export function useCreateDoctorAdminConversation() {
  const invalidatedQuery = useInvalidatedQuery();

  return useMutation({
    mutationFn: async (params: { patientId: string }) => {
      const response = await apiClient.post<{ conversationId: string }>(
        '/doctor-admin-chat/conversations',
        params,
      );
      return response.data;
    },
    onSuccess: (_, params) => {
      void invalidatedQuery(['patient', params.patientId, 'info']);
    },
  });
}
export function useReopenDoctorAdminConversation() {
  const invalidatedQuery = useInvalidatedQuery();
  return useMutation({
    mutationFn: async ({
      conversationId,
    }: {
      conversationId: string;
      patientId: string;
    }) => {
      const response = await apiClient.put(
        `/doctor-admin-chat/conversations/${conversationId}/reopen`,
      );
      return response.data;
    },
    onSuccess: (_, params) => {
      void invalidatedQuery(['patient', params.patientId, 'info']);
      void invalidatedQuery(['doctorAdmin', params.conversationId]);
      void invalidatedQuery(['chat', params.conversationId]);
      void invalidatedQuery(['watcher', params.conversationId]);
    },
  });
}
