'use client';

import Link from 'next/link';
import {
  useGetDashboardHomePageData,
  useMarkConversationAsRead,
} from '@/hooks/dashboard';
import { useQueryClient } from '@tanstack/react-query';
import { Image } from 'lucide-react';

import { cn } from '@willow/ui';
import { Button, buttonVariants } from '@willow/ui/base/button';

import type { Conversation, LastMessage } from '~/data/dashboard-types';
import { PatientsColumn } from '../_components/patients-column';
import { PatientCard } from './patient-card';

export const MessagesColumn = () => {
  const { data, isPending } = useGetDashboardHomePageData();

  return (
    <PatientsColumn
      count={data?.lastMessages?.length ?? 0}
      title="Messages"
      isLoading={isPending}
    >
      {data?.lastMessages.map((m) => <MessageCard key={m.id} message={m} />)}
    </PatientsColumn>
  );
};

const MessageCard = ({ message }: { message: LastMessage }) => {
  const queryClient = useQueryClient();
  const { mutate: markAsRead, isPending } = useMarkConversationAsRead({
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ['doctor', 'dashboard'] });
    },
  });

  // Check if any of the unread messages is a doctor note
  const hasDoctorNote = message.conversation.unreadMessages.some(
    (m) => m.type === 'doctorNote',
  );
  const isAdminConversation = message.conversation.type === 'doctorAdmin';

  return (
    <PatientCard
      patient={{
        firstName: message.conversation.patient.user.firstName,
        lastName: message.conversation.patient.user.lastName,
        stateCode: '',
        distanceDate: message.updatedAt,
        displayDate: message.updatedAt,
      }}
      topLineColor={
        hasDoctorNote ? 'warning' : isAdminConversation ? 'info' : 'denim'
      }
      Footer={
        <>
          <Button
            variant="destructive"
            size="sm"
            className="w-full rounded-none text-white"
            loading={isPending}
            onClick={() => markAsRead(message.conversation.id)}
          >
            Clear
          </Button>
          <Link
            href={`/patients/${message.conversation.patientId}/${isAdminConversation ? 'admin-messages' : 'messages'}`}
            className={cn(
              buttonVariants({ variant: 'denim', size: 'sm' }),
              'w-full rounded-none text-white',
            )}
          >
            Reply
          </Link>
        </>
      }
    >
      <div className="mb-4 hidden flex-col gap-1 px-2 group-hover:flex">
        {message.conversation.unreadMessages.map((m) => (
          <span className="text-sm text-black" key={m.id}>
            {m.type !== 'doctorNote' && (
              <span className="mr-2 font-semibold">
                {message.conversation.lastMessageUser?.firstName}:
              </span>
            )}
            <MessageContentExcerpt message={m} />
          </span>
        ))}
      </div>
    </PatientCard>
  );
};

const MessageContentExcerpt = ({
  message,
}: {
  message: Conversation['unreadMessages'][0];
}) => {
  if (message.contentType === 'image') {
    return (
      <span className="inline-flex items-center gap-1 rounded-full border bg-gray-100 px-2 py-1 text-xs">
        <Image size={'18px'} />
        Picture
      </span>
    );
  }

  // For doctor notes, we could add a visual indicator if needed
  if (message.type === 'doctorNote') {
    return (
      <span>
        {message.content.length > 50
          ? `${message.content.substring(0, 100)}...`
          : message.content}
      </span>
    );
  }

  return message.content.length > 50
    ? `${message.content.substring(0, 100)}...`
    : message.content;
};
