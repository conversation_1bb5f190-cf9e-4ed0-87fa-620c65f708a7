'use client';

import { formatInTimeZone } from 'date-fns-tz';
import { Check } from 'lucide-react';

import { formatBirthDate } from '@willow/utils/format';

import { useCurrentPatient } from '~/hooks/patient';

export function NonDocumentPatientInfo() {
  const patient = useCurrentPatient();

  if (!patient) {
    return <div className="p-14">Loading patient data...</div>;
  }

  const { user, birthDate, lastFourSSN } = patient;

  const patientInfo = [
    { label: 'First Name', value: user.firstName },
    { label: 'Last Name', value: user.lastName },
    { label: 'Date of Birth', value: formatBirthDate(new Date(birthDate)) },
    { label: 'Phone', value: user.phone || '************' },
    { label: 'State', value: patient.state?.code || 'TX' },
    { label: 'SSN', value: lastFourSSN || '1234' },
  ];

  return (
    <div>
      <h3 className="mb-4 text-base font-normal text-zinc-900">
        Patient personal information
      </h3>
      <div className="mb-4 overflow-hidden rounded-lg bg-gray-50">
        {patientInfo.map((info, index) => (
          <div key={index}>
            <div className="flex items-center justify-between p-4">
              <div className="flex-1">
                <div className="text-xs text-zinc-500">{info.label}</div>
                <div className="text-sm font-medium text-zinc-900">
                  {info.value}
                </div>
              </div>
              <Check className="h-5 w-5 rounded-full bg-green-500 p-1 text-white" />
            </div>
            {index < patientInfo.length - 1 && (
              <div className="mx-4 border-b border-gray-200"></div>
            )}
          </div>
        ))}
      </div>

      <div className="flex items-center gap-2">
        <Check className="h-5 w-5 rounded-full bg-green-500 p-1 text-white" />
        <div className="text-green-600">
          <span className="font-semibold">SUCCESS</span>
          <div className="text-xs text-zinc-500">
            {formatInTimeZone(
              patient.vouchedVerifiedAt ?? new Date(),
              'America/New_York',
              'h:mm a MM/dd/yy',
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
