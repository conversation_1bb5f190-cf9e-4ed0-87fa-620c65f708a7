import { useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { camelToKebab } from '@/lib/utils';
import { onboardingDataAtom, onboardingVersionAtom } from '@/store/store';
import { useAtom, useAtomValue } from 'jotai';

export const useOnboardingNavigation = () => {
  const [onboarding, setOnboarding] = useAtom(onboardingDataAtom);
  const onboardingVersion = useAtomValue(onboardingVersionAtom);
  const router = useRouter();

  const nextOnboardingStep = useCallback(
    (data: any) => {
      setOnboarding(data);
    },
    [setOnboarding],
  );

  useEffect(() => {
    if (onboarding) {
      let route: string;

      // Handle v1 state machine navigation
      if (onboardingVersion === 'v1' && typeof onboarding.state === 'string') {
        // Pre-signup states
        if (onboarding.state.startsWith('preSignup')) {
          const stateMap: Record<string, string> = {
            preSignup: '/account/state', // Handle compound state
            'preSignup.stateSelection': '/account/state',
            'preSignup.firstAndLastName': '/account/name',
            'preSignup.createAccount': '/account/signup',
            'preSignup.unsupportedState': '/account/unsupported',
            'preSignup.unsupportedStateThankYou': '/account/thank-you',
          };
          route = stateMap[onboarding.state] || '/account/state';
        }
        // Questionnaire states
        else if (onboarding.state.startsWith('questionnaire')) {
          // Handle both 'questionnaire' and 'questionnaire.age' formats
          if (onboarding.state === 'questionnaire') {
            route = '/onboarding/questionnaire/age';
          } else {
            const questionnaireState = onboarding.state.split('.')[1];
            route = questionnaireState
              ? `/onboarding/questionnaire/${camelToKebab(questionnaireState)}`
              : '/onboarding/questionnaire/age';
          }
        }
        // Post-questionnaire states
        else {
          // Handle specific state mappings for identity verification
          const stateMap: Record<string, string> = {
            identityVerification: '/onboarding/identity-verification',
            ssnCheck: '/onboarding/ssn-check',
            ssnSuccess: '/onboarding/ssn-success',
          };

          route =
            stateMap[onboarding.state] ||
            `/onboarding/${camelToKebab(onboarding.state)}`;
        }
      }
      // Handle legacy navigation
      else {
        if (typeof onboarding.state === 'string') {
          route = `/onboarding/${camelToKebab(onboarding.state)}`;
        } else {
          route = `/onboarding/questionnaire/${camelToKebab(onboarding.state.questionnaire)}`;
        }
      }

      router.push(route, { scroll: false });
    }
  }, [onboarding, onboardingVersion, router]);

  return { onboarding, nextOnboardingStep };
};
