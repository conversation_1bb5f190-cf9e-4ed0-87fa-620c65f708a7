import type { OnboardingProps } from '@/app/(connected)/onboarding/(onboarding)/questionnaire/[slug]/page';
import CheckboxListQuestion from '@/components/onboarding/CheckboxListQuestion';

const QualifyingConditions = (props: OnboardingProps) => {
  const config = {
    fieldName: 'qualifyingConditions',
    title: 'Do you have any of the following medical conditions?',
    subtitle: `If you have one of these medical conditions, please answer yes even if you're controlling it with treatment. If you have one of these conditions, you may qualify for medication with a lower BMI.`,
    options: [
      { id: 'highBloodPressure', label: 'High Blood Pressure' },
      { id: 'highLipids', label: 'High Lipids' },
      { id: 'highCholesterol', label: 'High Cholesterol' },
      { id: 'obstructiveSleepApnea', label: 'Obstructive Sleep Apnea' },
      { id: 'cardiovascularDisease', label: 'Cardiovascular Disease' },
      { id: 'anorexiaNervosa', label: 'Anorexia Nervosa' },
      { id: 'bulimiaNervosa', label: 'Bulimia Nervosa' },
    ],
    noneOption: { id: 'none', label: 'None' },
    getContextValue: (context: any) =>
      context.questionnaire.qualifyingConditions,
  };

  return <CheckboxListQuestion {...props} config={config} />;
};

export default QualifyingConditions;
