'use client';

import type { OnboardingData } from '@/data/types';
import { useMemo, useState } from 'react';
import Image from 'next/image';
import placeholderId from '@/assets/svg/placeholder-id.svg';
import placeholderPhoto from '@/assets/svg/placeholder-photo.svg';
import { useToast } from '@/components/ui/use-toast';
import { useAnalytics } from '@/context/AnalyticsContext';
import { useOnboardingNext } from '@/hooks/onboarding';
import { useSavePhoto, useUploadPhoto } from '@/hooks/uploadPhotos';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { onboardingDataAtom } from '@/store/store';
import classNames from 'classnames';
import { useAtom } from 'jotai/index';
import { CameraIcon, UploadIcon } from 'lucide-react';

import { env } from '~/env';
import { useLog } from '~/hooks/useLog';
import { Button } from '../ui/button';
import { InfoDialog } from '../ui/infoDialog';
import { useCamera } from './useCamera';

const map = {
  idPhoto: 'id-photo',
  facePhoto: 'face-photo',
} as const;

export function OnboardingPhotoUpload({
  type,
  onSuccessfulUpload,
}: {
  type: 'idPhoto' | 'facePhoto';
  onSuccessfulUpload?: () => void;
}) {
  const { log } = useLog();
  const analyticsData = useAnalyticsData();
  const analytics = useAnalytics();
  const { toast } = useToast();
  const [onboarding, setOnboarding] = useAtom(onboardingDataAtom);

  const isIdPhoto = type === 'idPhoto';
  const placeholder = isIdPhoto ? placeholderId : placeholderPhoto;

  const [showInfoModal, setShowInfoModal] = useState(false);
  const [file, setFile] = useState<File>();

  const { mutateAsync: upload, isPending: _isUploading } = useUploadPhoto(
    'onboarding',
    map[type],
  );

  const { mutateAsync: uploadOnboarding } = useSavePhoto<OnboardingData>(
    'onboarding',
    map[type],
  );

  const { mutateAsync: next, isPending: isSaving } = useOnboardingNext();

  const isUploading = _isUploading || isSaving;

  const { Input, startCamera, selectPhoto } = useCamera({
    defaultCaptureType: type === 'idPhoto' ? 'environment' : 'user',
    onFileChange: (file) => {
      setFile(file);
    },
    isUploading: isUploading,
  });

  const photoUrl = useMemo(() => {
    const photoPath = onboarding?.context[map[type]];
    if (!photoPath) return undefined;
    return `${env.NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL}/${photoPath}?t=${Date.now()}`;
  }, [type, onboarding]);

  const previewUrl = useMemo(() => {
    return file ? URL.createObjectURL(file) : (photoUrl ?? null);
  }, [file, photoUrl]);

  const onSubmit = async () => {
    try {
      if (!file) return;
      await upload(file);
      const response = await uploadOnboarding({ skip: false });
      setOnboarding(response.data);
      void analytics?.track(
        isIdPhoto ? 'ID Uploaded' : 'Face Photo Uploaded',
        analyticsData,
      );
      onSuccessfulUpload?.();
    } catch (e) {
      console.error(e);
      toast({
        title: 'Error uploading photo',
        description: 'Please try again',
        variant: 'destructive',
      });
      log({
        aggregateId: 'onboarding:photo-upload',
        event: `upload-${type}-error`,
        error: e as Error,
      });
    }
  };

  const onSkip = async () => {
    try {
      const response = await next({});
      setOnboarding(response.data);
      void analytics?.track(
        isIdPhoto ? 'IDUploadSkipped' : 'Photo Upload Skipped',
        analyticsData,
      );
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error skipping photo upload',
        description: 'Please try again',
        variant: 'destructive',
      });
      log({
        aggregateId: 'onboarding:photo-upload',
        event: `upload-${type}-skip-error`,
        error: error as Error,
      });
    }
  };

  if (!onboarding) return null;

  return (
    <div className="flex flex-col gap-3">
      <div className="flex flex-col gap-10 pb-4 md:pb-0">
        <div className="inline-flex w-full flex-col items-center justify-center gap-2.5 rounded-2xl border border-slate-500 bg-slate-500 py-10">
          {!previewUrl && (
            <Image
              className="h-52 w-full"
              src={placeholder}
              alt={'placeholder'}
            />
          )}
          {previewUrl && (
            <div
              className="h-52 w-full rounded-lg bg-contain bg-center bg-no-repeat"
              style={{
                backgroundImage: `url(${previewUrl})`,
              }}
            ></div>
          )}
        </div>
        <div>
          <Input />
          <div className="flex w-full flex-col gap-4">
            <Button
              onClick={startCamera}
              className="w-full gap-2 px-12 text-lg font-semibold md:w-auto"
              variant="outlineWhite"
              size={'lg'}
              disabled={isUploading}
            >
              <span>
                <CameraIcon width={24} height={24} />
              </span>
              Take Photo
            </Button>
            <Button
              onClick={selectPhoto}
              className={classNames(
                'w-full gap-2 px-12 text-lg font-semibold md:w-auto',
              )}
              variant={file || previewUrl ? 'outlineWhite' : 'electric'}
              size={'lg'}
              disabled={isUploading}
            >
              <span>
                <UploadIcon width={24} height={24} />
              </span>
              <span>Select Photo</span>
            </Button>

            {(file || previewUrl) && (
              <Button
                onClick={file ? onSubmit : onSkip}
                className={classNames(
                  'w-full gap-2 px-12 text-lg font-semibold md:w-auto',
                )}
                variant={'electric'}
                size={'lg'}
                disabled={isUploading}
              >
                Continue
              </Button>
            )}
          </div>
        </div>
      </div>

      <div
        className={classNames(
          'flex flex-col text-center text-white md:mt-8 md:gap-4',
        )}
      >
        <a
          href="#"
          className="underline"
          onClick={() => setShowInfoModal(true)}
        >
          Having Trouble?
        </a>

        <span className="">256-BIT TLS SECURITY</span>
      </div>

      {showInfoModal && (
        <InfoDialog
          title="Having Trouble?"
          description="You can bypass uploading now but you will need to upload it later in order for your local provider to verify your identity."
          confirmBtnText="UPLOAD NOW"
          confirmBtnClick={() => setShowInfoModal(false)}
          closeBtnClick={() => {
            void onSkip();
            setShowInfoModal(false);
          }}
          closeBtnText="UPLOAD LATER"
          variant={'dark'}
        />
      )}
    </div>
  );
}
