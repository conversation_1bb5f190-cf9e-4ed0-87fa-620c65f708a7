import type { VariantProps } from 'class-variance-authority';
import * as React from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { X } from 'lucide-react';

import { Button } from './button';

const dialogVariants = cva('', {
  variants: {
    variant: {
      default: 'bg-white',
      dark: 'bg-denim',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

const dialogBgVariants = cva('', {
  variants: {
    variant: {
      default: 'bg-slate-600 bg-opacity-50',
      dark: 'bg-black bg-opacity-50 backdrop-blur-sm',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

interface DialogProps extends VariantProps<typeof dialogVariants> {
  children: React.ReactNode;
  className?: string;
  bg?: string;
  onCloseClick?: () => void;
  showClose?: boolean;
  onClickOutside?: () => void;
}

const Dialog = ({
  children,
  variant,
  onCloseClick,
  showClose,
  className,
  onClickOutside,
}: DialogProps) => (
  <>
    <div
      className={cn(
        dialogBgVariants({ variant }),
        'fixed inset-0 z-[20] transition-opacity',
      )}
      onClick={onClickOutside}
    ></div>

    <div className="fixed inset-0 top-[98px] z-[21] w-screen overflow-y-auto md:top-[150px]">
      <div
        className="flex min-h-full items-center justify-center text-center"
        onClick={onClickOutside}
      >
        <div
          className={cn(
            dialogVariants({ variant }),
            `relative transform overflow-hidden rounded-lg p-10 transition-all`,
            {
              ['md:px-14']: showClose,
            },
            className,
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {showClose && (
            <Button
              onClick={onCloseClick}
              size="icon"
              variant="link"
              className={`absolute right-3 top-3 z-[22] ${variant === 'dark' ? 'text-white' : 'text-[#2f4c78]'}`}
            >
              <X />
            </Button>
          )}

          {children}
        </div>
      </div>
    </div>
  </>
);

Dialog.displayName = 'Dialog';

export { Dialog };
