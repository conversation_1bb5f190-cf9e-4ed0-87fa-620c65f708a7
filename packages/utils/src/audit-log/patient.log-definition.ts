import { z } from 'zod';

import { zActivityAction, zAuditLogEntity } from './types';

export const zPatientAccoutCreatedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_ACCOUNT_CREATED),
  details: z.object({
    date: z.string(),
    state: z.string(),
  }),
});

export const zUserPasswordResetRequestedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.USER_PASSWORD_RESET_REQUESTED),
  details: z.object({
    date: z.string(),
  }),
});

export const zUserPasswordResetAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.USER_PASSWORD_RESET),
  details: z.object({
    date: z.string(),
  }),
});

export const zPatientVisitStartedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_VISIT_STARTED),
  details: z.object({
    email: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    birthdate: z.string(),
    gender: z.string(),
    firstCampaign: z.record(z.string(), z.any()).optional(),
  }),
});

export const zPatientIdPhotoUpdatedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_ID_PHOTO_UPDATED),
  details: z.object({
    type: z.enum(['id-photo', 'face-photo']),
    file: z.string(),
  }),
});

export const zPatientDoctorAssignedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_ACCEPTED),
  actorType: z.literal(zAuditLogEntity.Values.DOCTOR),
  details: z.object({
    doctorId: z.string(),
    doctorName: z.string(),
    doseSpotPatientId: z.string(),
  }),
});

export const zPatientAcceptedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_IDENTITY_ACCEPTED),
  actorType: z.literal(zAuditLogEntity.Values.DOCTOR),
  details: z.object({}),
});

export const zPatientRejectedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_REJECTED),
  actorType: z.literal(zAuditLogEntity.Values.DOCTOR),
  details: z.object({
    status: z.string(),
    reason: z.string(),
  }),
});

export const zPatientCancelledAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_CANCELLED),
  details: z.object({
    statusBeforeCancellation: z.string(),
    note: z.string(),
    reason: z.string(),
  }),
});

export const zPatientUnCancelledAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_UNCANCELLED),
  details: z.object({}),
});

export const zPatientDeletedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_DELETED),
  details: z.object({}),
});

const zShippingAddress = z.object({
  address1: z.string(),
  address2: z.string().optional(),
  city: z.string(),
  state: z.string(),
  zip: z.string(),
});

export const zPatientShippingAddressUpdatedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_SHIPPING_ADDRESS_UPDATED),
  details: z.object({
    old: zShippingAddress,
    changes: zShippingAddress.partial(),
  }),
});

export const zPatientBillingAddressUpdatedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_BILLING_ADDRESS_UPDATED),
  details: z.object({
    old: zShippingAddress,
    changes: zShippingAddress.partial(),
  }),
});

const zPatientProfile = z.object({
  firstName: z.string(),
  lastName: z.string(),
  birthDate: z.string(),
  phone: z.string(),
  email: z.string(),
  status: z.string(),
});
export const zPatientProfileInfoAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_PROFILE_INFO_UPDATED),
  details: z.object({
    old: zPatientProfile,
    changes: zPatientProfile.partial(),
  }),
});

const zPatientPaymentInfo = z.object({
  cardLast4: z.string(),
  cardType: z.string(),
  cardExpMonth: z.string(),
  cardExpYear: z.string(),
});
export const zPatientPaymentInfoUpdatedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_PAYMENT_INFO_UPDATED),
  details: z.object({
    type: z.string(),
    old: zPatientPaymentInfo,
    new: zPatientPaymentInfo.optional(),
  }),
});

export const zPatientInfoUpdatedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_INFO_UPDATED),
  details: z.object({
    old: z.record(z.string(), z.unknown()),
    changes: z.record(z.string(), z.unknown()),
  }),
});

const zDoctorInfo = z.object({
  doctorId: z.string(),
  firstName: z.string(),
  lastName: z.string(),
});

export const zPatientReassignDoctorAuditLog = z.object({
  action: z.literal(zActivityAction.Values.PATIENT_REASSIGNED_DOCTOR),
  actorType: z.literal(zAuditLogEntity.Values.ADMIN),
  actorId: z.string(),
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  details: z.object({
    old: zDoctorInfo,
    new: zDoctorInfo,
  }),
});

export const zPatientRefundOnCancelAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_REFUND_ON_CANCEL),
  actorType: z.literal(zAuditLogEntity.Values.SYSTEM),
  actorId: z.string(),
  details: z.object({
    refundedInvoices: z.number(),
    totalRefundedAmount: z.number(),
  }),
});
export const zPatientRefundIssuedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_REFUND_ISSUED),
  actorType: z.literal(zAuditLogEntity.Values.SYSTEM),
  actorId: z.string(),
  details: z.object({
    refundedValue: z.number(),
    chargeValue: z.number(),
    capturedValue: z.number(),
    cardLast4: z.string(),
    cardName: z.string(),
    productNames: z.array(z.string()),
    originalPaymentDate: z.string(),
  }),
});

export const zPatientCreateNoteAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  actorType: z.literal(zAuditLogEntity.Values.ADMIN),
  actorId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_NOTE_CREATED),
  details: z.object({
    note: z.string(),
  }),
});

export const zPatientInvoicePaidAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_INVOICE_PAID),
  details: z.object({
    patientName: z.string(),
    doctorName: z.string(),
    doctorId: z.string(),
    stripeCustomerId: z.string(),
    stripeInvoiceId: z.string(),
    total: z.number(),
    amountPaid: z.number(),
    items: z.array(
      z.object({
        name: z.string(),
        priceId: z.string(),
        treatmentId: z.string(),
        prescriptionId: z.string(),
        price: z.number(),
      }),
    ),
    invoiceId: z.string(),
    cardType: z.string(),
    cardLast4: z.string(),
    coupon: z.string().optional(),
    products: z.array(
      z.object({
        id: z.string(),
        price: z.number(),
        name: z.string(),
      }),
    ),
  }),
});

export const zPatientInvoicePaymentFailedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PATIENT_INVOICE_PAYMENT_FAILED),
  details: z.object({
    stripeInvoiceId: z.string(),
    message: z.string(),
    patientId: z.string(),
    patientName: z.string(),
    doctorId: z.string(),
    doctorName: z.string(),
    items: z.array(
      z.object({
        name: z.string(),
        priceId: z.string(),
        treatmentId: z.string(),
        prescriptionId: z.string(),
        price: z.number(),
      }),
    ),
    products: z.array(
      z.object({
        id: z.string(),
        price: z.number(),
        name: z.string(),
      }),
    ),
  }),
});

export const zPrescriptionTransferredAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.PATIENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.PRESCRIPTION_TRANSFERRED),
  actorType: z.literal(zAuditLogEntity.Values.SYSTEM),
  details: z.object({
    state: z.string(),
    doctorName: z.string(),
    oldPharmacy: z.string(),
    newPharmacy: z.string(),
    productType: z.enum(['Semaglutide', 'Tirzepatide', 'Other']),
    productForm: z.enum(['oral', 'injectable', 'tablet']),
    status: z.enum(['Complete', 'Passed']).optional(),
  }),
});

export const patientAuditLogDefinitions = [
  zPatientAccoutCreatedAuditLog,
  zUserPasswordResetRequestedAuditLog,
  zUserPasswordResetAuditLog,
  zPatientVisitStartedAuditLog,
  zPatientIdPhotoUpdatedAuditLog,
  zPatientDoctorAssignedAuditLog,
  zPatientDeletedAuditLog,
  zPatientAcceptedAuditLog,
  zPatientRejectedAuditLog,
  zPatientCancelledAuditLog,
  zPatientUnCancelledAuditLog,
  zPatientShippingAddressUpdatedAuditLog,
  zPatientBillingAddressUpdatedAuditLog,
  zPatientProfileInfoAuditLog,
  zPatientPaymentInfoUpdatedAuditLog,
  zPatientInfoUpdatedAuditLog,
  zPatientReassignDoctorAuditLog,
  zPatientRefundOnCancelAuditLog,
  zPatientCreateNoteAuditLog,
  zPatientRefundIssuedAuditLog,
  zPatientInvoicePaidAuditLog,
  zPatientInvoicePaymentFailedAuditLog,
  zPrescriptionTransferredAuditLog,
] as const;
