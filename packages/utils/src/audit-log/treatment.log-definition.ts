import { z } from 'zod';

import { zActivityAction, zAuditLogEntity } from './types';

export const zTreatmentCreatedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_CREATED),
  details: z.object({
    doctorId: z.string(),
    doctorName: z.string(),
    patientId: z.string(),
    patientFirstName: z.string(),
    patientLastName: z.string(),
    pharmacyName: z.string(),
    productName: z.string(),
    couponCode: z.string().optional(),
    delayUntil: z.string().optional(),
    initialProductPriceId: z.string(),
    finalProductPriceId: z.string(),
    refills: z.number(),
    refillSystem: z.string(),
    vials: z.number(),
    notes: z.string().optional(),
    shortInitialPrescription: z.boolean().optional(),
    isTransfer: z.boolean(),
  }),
});

export const zTreatmentInvoicePaidAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_INVOICE_PAID),
  details: z.object({
    patientName: z.string(),
    doctorName: z.string(),
    doctorId: z.string(),
    stripeCustomerId: z.string(),
    stripeInvoiceId: z.string(),
    total: z.number().optional(),
    amountPaid: z.number().optional(),
    invoiceId: z.string(),
    cardType: z.string(),
    cardLast4: z.string(),
    coupon: z.string().optional(),
    products: z
      .array(
        z.object({
          id: z.string(),
          price: z.number(),
          name: z.string(),
        }),
      )
      .optional(),
  }),
});

export const zTreatmentInvoicePaymentFailedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_INVOICE_PAYMENT_FAILED),
  details: z.object({
    stripeInvoiceId: z.string(),
    message: z.string(),
    patientId: z.string(),
    patientName: z.string(),
    doctorId: z.string(),
    doctorName: z.string(),
    product: z
      .object({
        id: z.string(),
        price: z.number(),
        name: z.string(),
      })
      .optional(),
  }),
});

export const zTreatmentOrderSentAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_ORDER_SENT),
  details: z.object({
    patientId: z.string(),
    patientName: z.string(),
    doctorId: z.string(),
    doctorName: z.string(),
    orderId: z.string(),
    eipOrderId: z.number().optional(),
    pharmacy: z.string(),
    form: z.string(),
    product: z.object({
      id: z.string(),
      name: z.string(),
      quantity: z.number(),
    }),
  }),
});

export const zTreatmentOrderFailedAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_ORDER_FAILED),
  details: z.object({
    patientId: z.string(),
    patientName: z.string(),
    doctorId: z.string(),
    doctorName: z.string(),
    orderId: z.string(),
    error: z.string(),
    product: z.object({
      id: z.string(),
      name: z.string(),
      quantity: z.number(),
    }),
  }),
});

export const zTreatmentOrderSentManuallyAuditLog = z.object({
  patientId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_ORDER_SENT_MANUALLY),
  details: z.object({
    patientId: z.string(),
    patientName: z.string(),
    doctorId: z.string(),
    doctorName: z.string(),
    pharmacy: z.string(),
    form: z.string(),
    product: z.object({
      id: z.string(),
      name: z.string(),
      quantity: z.number(),
    }),
  }),
});

export const zResumeTreatmentAuditLog = z.object({
  patientId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_RESUMED),
  actorType: z.enum([
    zAuditLogEntity.Values.ADMIN,
    zAuditLogEntity.Values.DOCTOR,
  ]),
  actorId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  actorExtraDetails: z.object({
    firstName: z.string(),
    lastName: z.string(),
  }),
  details: z.object({}),
});

export const zPauseTreatmentAuditLog = z.object({
  patientId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_PAUSED),
  actorType: z.enum([
    zAuditLogEntity.Values.ADMIN,
    zAuditLogEntity.Values.DOCTOR,
  ]),
  actorId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  actorExtraDetails: z.object({
    firstName: z.string(),
    lastName: z.string(),
  }),
  details: z.object({}),
});

export const zFireNextTreatmentAuditLog = z.object({
  patientId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_FIRED_NEXT),
  actorType: z.enum([
    zAuditLogEntity.Values.ADMIN,
    zAuditLogEntity.Values.DOCTOR,
  ]),
  actorId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  actorExtraDetails: z.object({
    firstName: z.string(),
    lastName: z.string(),
  }),
  details: z.object({}),
});

export const zCancelTreatmentAuditLog = z.object({
  patientId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_CANCELLED),
  actorType: z.enum([
    zAuditLogEntity.Values.ADMIN,
    zAuditLogEntity.Values.DOCTOR,
  ]),
  actorId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  actorExtraDetails: z.object({
    firstName: z.string(),
    lastName: z.string(),
  }),
  details: z.object({}),
});

export const zMoveRefillDateTreatmentAuditLog = z.object({
  patientId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_REFILL_DATE_MOVED),
  actorType: z.enum([
    zAuditLogEntity.Values.ADMIN,
    zAuditLogEntity.Values.DOCTOR,
  ]),
  actorId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  actorExtraDetails: z.object({
    firstName: z.string(),
    lastName: z.string(),
  }),
  details: z.object({}),
});

export const zTreatmentRetryFailedPaymentAuditLog = z.object({
  patientId: z.string(),
  action: z.literal(zActivityAction.Values.TREATMENT_RETRY_FAILED_PAYMENT),
  actorType: z.enum([
    zAuditLogEntity.Values.ADMIN,
    zAuditLogEntity.Values.DOCTOR,
  ]),
  actorId: z.string(),
  resourceType: z.literal(zAuditLogEntity.Values.TREATMENT),
  resourceId: z.string(),
  actorExtraDetails: z.object({
    firstName: z.string(),
    lastName: z.string(),
  }),
  details: z.object({}),
});

export const treatmentLogDefinitions = [
  zTreatmentCreatedAuditLog,
  zTreatmentInvoicePaidAuditLog,
  zTreatmentInvoicePaymentFailedAuditLog,
  zTreatmentOrderSentAuditLog,
  zTreatmentOrderFailedAuditLog,
  zTreatmentOrderSentManuallyAuditLog,
  zResumeTreatmentAuditLog,
  zPauseTreatmentAuditLog,
  zFireNextTreatmentAuditLog,
  zCancelTreatmentAuditLog,
  zMoveRefillDateTreatmentAuditLog,
  zTreatmentRetryFailedPaymentAuditLog,
] as const;
