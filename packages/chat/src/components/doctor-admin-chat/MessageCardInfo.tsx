import React from 'react';
import { format } from 'date-fns';

import type { ChatParticipant, Message } from '@willow/chat';
import { cn } from '@willow/ui';
import { Avatar, AvatarFallback, AvatarImage } from '@willow/ui/base/avatar';

import { convertUrlsToLinks } from '../../helpers';
import { ImgMessage } from './ImgMessage';

export const MessageCardInfo = ({
  message,
  participants,
  profileId,
  genAssetLink,
}: {
  message: Message;
  participants: Record<string, ChatParticipant>;
  profileId: string;
  genAssetLink: (path: string | null | undefined) => string;
}) => {
  const isCurrentUser = profileId === message.userId;

  const participant = message.userId ? participants[message.userId] : undefined;

  return (
    <div className="flex flex-col gap-2">
      <div
        className={cn('flex flex-row items-center gap-2', {
          'flex-row-reverse': isCurrentUser,
        })}
      >
        <Avatar className="h-10 w-10">
          <AvatarImage src={genAssetLink(participant?.imageUrl)} />
          <AvatarFallback className="font-bold uppercase text-denim-light">
            {participant?.user.firstName[0]}
            {participant?.user.lastName[0]}
          </AvatarFallback>
        </Avatar>
        <div className="text-sm font-medium text-denim">{`${participant?.user.firstName} ${participant?.user.lastName}`}</div>
      </div>

      <div
        className={cn(
          'w-fit min-w-32 rounded-lg bg-stone-light px-[10px] py-2',
          {
            'bg-[#48638B]': isCurrentUser,
            'self-end': isCurrentUser,
          },
        )}
      >
        {message.contentType === 'image' ? (
          <ImgMessage message={message} />
        ) : (
          <div
            className={cn('mb-3 text-xs font-normal text-zinc-900', {
              'text-white': isCurrentUser,
            })}
          >
            {renderTextMessages(message.content)}
          </div>
        )}

        <div
          className={cn('w-f text-[9px] font-normal text-zinc-500', {
            'text-white': isCurrentUser,
          })}
        >
          {format(new Date(message.createdAt), 'hh:mm a')}
        </div>
      </div>
    </div>
  );
};

function renderTextMessages(text: string) {
  const lines = text.split('\n');
  return (
    <p>
      {lines.map((line, index) => (
        <React.Fragment key={index}>
          {convertUrlsToLinks(line)}
          <br />
        </React.Fragment>
      ))}
    </p>
  );
}
