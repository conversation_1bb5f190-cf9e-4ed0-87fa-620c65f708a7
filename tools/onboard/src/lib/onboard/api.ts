import { Agent } from 'http';
import type { AxiosError, AxiosInstance, AxiosRequestConfig } from 'axios';
import axios from 'axios';

const DELAY_BETWEEN_REQUESTS = 50;

interface SignupResponse {
  accessToken: string;
  patientId: string;
}

// Response type for pre-signup endpoints
interface PreSignupResponse {
  currentState: string;
  currentStep: number;
  totalSteps: number;
  canTransition: boolean;
  isComplete: boolean;
  context?: unknown;
}

// Response type after account creation (PatientSignInOutput)
interface AuthenticatedResponse {
  accessToken: string;
  refreshToken: string;
  role: string;
  status: string;
  patientId: string;
  onboarding?: {
    state: unknown;
    context: unknown;
    currentStep: number;
    totalSteps: number;
    stepName: string;
  };
  dashboard?: unknown;
  getStarted: {
    email: string;
    phone?: string;
    state?: string;
    firstName?: string;
    lastName?: string;
    gender?: string;
    birthday?: string;
  };
}

export class OnboardingClient {
  protected client: AxiosInstance;
  protected accessToken: string | null = null;
  protected cookies: string[] = [];
  private lastRequestTime = 0;

  constructor(
    baseUrl = process.env.API_URL ??
      `http://localhost:${process.env.API_PORT ?? '8081'}`,
  ) {
    this.client = axios.create({
      baseURL: baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 300000,
      httpAgent: new Agent({
        keepAlive: true,
        timeout: 60000,
      }),
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      withCredentials: true,
    });

    // Add request logging
    this.client.interceptors.request.use(async (config) => {
      // Add delay if needed
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;
      if (timeSinceLastRequest < DELAY_BETWEEN_REQUESTS) {
        await new Promise((resolve) =>
          setTimeout(resolve, DELAY_BETWEEN_REQUESTS - timeSinceLastRequest),
        );
      }
      this.lastRequestTime = Date.now();

      // Add cookies to headers
      if (this.cookies.length > 0) {
        config.headers.Cookie = this.cookies.join('; ');
      }

      // Add auth header
      if (this.accessToken) {
        config.headers.Authorization = `Bearer ${this.accessToken}`;
      }
      return config;
    });

    // Add response logging and retry logic
    this.client.interceptors.response.use(
      (response) => {
        // Extract and store cookies from Set-Cookie headers
        const setCookieHeaders = response.headers['set-cookie'];
        if (setCookieHeaders) {
          this.cookies = setCookieHeaders
            .map((cookie: string) => {
              // Extract just the cookie name=value part
              return cookie.split(';')[0];
            })
            .filter((cookie): cookie is string => cookie !== undefined);
        }
        return response;
      },
      async (error: AxiosError) => {
        const isNetworkError =
          error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT';
        const config = error.config as AxiosRequestConfig & {
          headers?: { __isRetry?: boolean };
        };

        if (isNetworkError && !config.headers?.__isRetry) {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          config.headers = { ...config.headers, __isRetry: true };
          return this.client.request(config);
        }
        throw error;
      },
    );
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await this.client.get('/healthcheck');
      return response.status === 200;
    } catch {
      return false;
    }
  }

  // New pre-signup flow methods
  async initialize(): Promise<PreSignupResponse> {
    const response = await this.client.post<PreSignupResponse>(
      '/onboarding/initialize',
    );
    return response.data;
  }

  async preSignupSetState(state: string): Promise<PreSignupResponse> {
    const response = await this.client.post<PreSignupResponse>(
      '/onboarding/pre-signup/set-state',
      { state },
    );
    return response.data;
  }

  async preSignupName(
    firstName: string,
    lastName: string,
  ): Promise<PreSignupResponse> {
    const response = await this.client.post<PreSignupResponse>(
      '/onboarding/pre-signup/name',
      { firstName, lastName },
    );
    return response.data;
  }

  async preSignupCreateAccount(payload: {
    email: string;
    password: string;
    phone: string;
    getPromotionsSMS: boolean;
  }): Promise<AuthenticatedResponse> {
    const response = await this.client.post<AuthenticatedResponse>(
      '/onboarding/pre-signup/create-account',
      payload,
    );
    // Store access token if available
    if (response.data.accessToken) {
      this.accessToken = response.data.accessToken;
    }
    return response.data;
  }

  // Deprecated - kept for backward compatibility if needed
  async signup(payload: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phone: string;
    state: string;
    getPromotionsSMS: boolean;
  }): Promise<SignupResponse> {
    const response = await this.client.post<SignupResponse>(
      '/patient/signup',
      payload,
    );
    this.accessToken = response.data.accessToken;
    return response.data;
  }

  async submitQuestionnaireStep(
    event: string,
    value?: Record<string, unknown>,
  ): Promise<void> {
    const payload = value ? { event, value } : { event };
    await this.client.post('/onboarding/questionnaire', payload);
  }

  async getTreatmentTypes() {
    const response = await this.client.get('/onboarding/treatment-types');
    return response.data as { id: string }[];
  }

  async setDesiredTreatmentType(categoryId: string): Promise<void> {
    await this.client.post('/onboarding/desired-treatment-type', {
      categoryId,
    });
  }

  async getRecommendedTreatment(type: string) {
    const response = await this.client.get(
      `/onboarding/recommended-treatment/${type}`,
    );
    return response.data as {
      products: { id: string; type: 'core' | 'additional' }[];
      usesGLP1: boolean;
    };
  }

  async setDesiredTreatment(products: string[], vials: number): Promise<void> {
    await this.client.post('/onboarding/desired-treatment', {
      products,
      vials,
    });
  }

  async confirmPhotoUpload(
    type: 'id-photo' | 'face-photo',
    skip = false,
  ): Promise<void> {
    await this.client.post('/onboarding/photo', { type, skip });
  }

  async submitShippingInfo(payload: {
    address1: string;
    address2: string;
    city: string;
    state: string;
    zip: string;
    force?: boolean;
  }): Promise<void> {
    await this.client.post('/onboarding/shipping-info', payload);
  }

  async setupPaymentIntent(): Promise<void> {
    await this.client.post('/onboarding/setup-payment-intent');
  }

  async next(): Promise<void> {
    await this.client.post('/onboarding/next');
  }

  async skipSsnCheck(): Promise<void> {
    await this.client.post('/onboarding/skip-ssn');
  }

  // Utility methods for questionnaire steps
  async submitBirthDate(birthDate: string): Promise<void> {
    await this.submitQuestionnaireStep('next', { birthDate });
  }

  async submitGender(gender: 'female' | 'male'): Promise<void> {
    await this.submitQuestionnaireStep(gender);
  }

  async submitPregnancyStatus(isPregnant: boolean): Promise<void> {
    await this.submitQuestionnaireStep(isPregnant ? 'yes' : 'no');
  }

  async submitGLP1Status(using: boolean): Promise<void> {
    await this.submitQuestionnaireStep(using ? 'yes' : 'no');
  }

  async submitDiabetesStatus(has: boolean): Promise<void> {
    await this.submitQuestionnaireStep(has ? 'yes' : 'no');
  }

  async submitDoctorVisits(has: boolean): Promise<void> {
    await this.submitQuestionnaireStep(has ? 'yes' : 'no');
  }

  async submitQualifyingConditions(conditions: string[]): Promise<void> {
    await this.submitQuestionnaireStep('next', {
      qualifyingConditions: conditions,
    });
  }

  async submitHeight(height: number): Promise<void> {
    await this.submitQuestionnaireStep('next', { height });
  }

  async submitWeight(weight: number): Promise<void> {
    await this.submitQuestionnaireStep('next', { weight });
  }

  async submitDesiredWeight(desiredWeight: number): Promise<void> {
    await this.submitQuestionnaireStep('next', { desiredWeight });
  }

  async submitObjectives(objectives: string[]): Promise<void> {
    await this.submitQuestionnaireStep('next', { objectives });
  }

  async submitAllergiesStatus(has: boolean): Promise<void> {
    await this.submitQuestionnaireStep(has ? 'yes' : 'no');
  }

  async submitAllergies(allergies: string[]): Promise<void> {
    await this.submitQuestionnaireStep('next', { allergies });
  }

  async submitMedications(medications: string[]): Promise<void> {
    await this.submitQuestionnaireStep('next', { medications });
  }

  async submitMedicalConditions(conditions: string[]): Promise<void> {
    await this.submitQuestionnaireStep('next', {
      medicalConditions: conditions,
    });
  }

  async submitAdditionalInfo(info: string): Promise<void> {
    await this.submitQuestionnaireStep('next', { additionalInformation: info });
  }
}
